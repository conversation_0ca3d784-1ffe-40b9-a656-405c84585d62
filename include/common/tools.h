/****************************************************************************************
 * 文 件 名 : tools.hpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 : rtxdr
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设    计 : zhengsw      '2018-03-10
 * 编    码 : zhengsw      '2018-03-10
 * 修    改 :
 ****************************************************************************************
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 公司介绍及版权说明
 *
 *           (C)Copyright 2018 YView    Corporation All Rights Reserved.
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 ****************************************************************************************/

#ifndef _COMM_TOOLS_H_
#define _COMM_TOOLS_H_

#include <unistd.h>
#include <errno.h>
#include <dirent.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <limits.h>
#include <sys/types.h>
#include <typeinfo>
#include <string.h>

#include <iostream>
#include <iomanip>
#include <ctime>
#include <vector>
#include <functional>

#include "iconv.h"

// 数组长度
#define dimen_of(x) (sizeof(x) / sizeof((x)[0]))

// check_nor_exit
#define CHECK_NOR_EXIT(cond, errMsg, retCode) if ((cond)) \
    {                                                     \
        fprintf(stderr, "%s\n", errMsg);                  \
        return (retCode);                                 \
    }

/****************************************************************************************
 * 函 数 名 : be2le
 * 功    能 : 大端序转换为小端序数值
 * 输入参数 : num: 大端序数值
 * 输出参数 : 无
 * 返 回 值 : 小端序数值
 ***************************************************************************************/
template <class T>
inline T be2le(const T &num)
{
    T         ret = 0;
    uint8_t * src = (uint8_t *)&num;
    uint8_t*  dst = (uint8_t *)&ret;

    for(uint i = 0; i < sizeof(T); i++)
    {
        dst[sizeof(T) - i - 1] = src[i];
    }

    return ret;
}

/****************************************************************************************
 * 函 数 名 : codeConvert
 * 功    能 :
 * 输入参数 : strFrom:
 * 输入参数 : strTo:
 * 输出参数 : 无
 * 返 回 值 : YV_SUCCSS: 成功; YV_FAIL: 失败
 ***************************************************************************************/
static inline
int codeConvert(const std::string &strFrom, std::string &strTo)
{
    iconv_t  icd        = 0;
    int      lSts       = 0;
    size_t   strFromLen = strFrom.size();
    size_t   strToLen   = strFromLen * 3;
    char    *pFrom      = (char *)strFrom.data();
    char    *pTo        = NULL;

    /* 检查空输入 */
    if (strFrom.empty())
    {
        strTo = strFrom;
        return 0;
    }

    std::vector<char>  bufTo(strToLen);
    pTo = &bufTo[0];

    icd = iconv_open("UTF-8", "UTF-16BE");
    if ((iconv_t)-1 == icd)
    {
        return -1;
    }

    lSts = iconv(icd, &pFrom, &strFromLen, &pTo, &strToLen);
    if (-1 == lSts)
    {
        iconv_close(icd);
        return -1;
    }

    strTo.assign(&bufTo[0]);
    iconv_close(icd);
    return lSts;
}

inline
void unixTime2Str(int time, std::string &strTime)
{
    char strTimeBuf[30] = { 0 };
    struct tm *tm = localtime((time_t *)&time);

    strftime(strTimeBuf, sizeof strTimeBuf, "%Y-%m-%d %H:%M:%S", tm);
    strTime.assign(strTimeBuf);
}

/****************************************************************************************
 * 函 数 名 : hundredThousandthSecond
 * 功    能 : 10 万分之一秒
 * 输出参数 : 无
 * 返 回 值 : YV_SUCCSS: 成功; YV_FAIL: 失败
 ***************************************************************************************/
inline
uint hundredThousandthSecond()
{
    struct timespec ts;

    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_nsec / 10000;
}

/*
 * print data in rows of 16 bytes: offset   hex   ascii
 *
 * 00000   47 45 54 20 2f 20 48 54  54 50 2f 31 2e 31 0d 0a   GET / HTTP/1.1..
 */
static inline
void print_as_hex(const u_char *payload, int len)
{
    int           i;
    const u_char *ch;

    /* hex */
    ch = payload;
    for(i = 0; i < len; i++) {
        printf("%02x ", *ch);
        ch++;
    }

    printf("\n");

    return;
}


inline
int forDirEntry(const char *dir_name, std::function <int(const char *, bool)> func)
{
    if( NULL == dir_name )
    {
        return -EINVAL;
    }

    /* check dir type */
    struct stat s;
    lstat( dir_name , &s );
    if( ! S_ISDIR( s.st_mode ) )
    {
        return -EINVAL;
    }

    /* open dir */
    dirent *pDirEntry = NULL;
    DIR    *pDir      = NULL;

    pDir = opendir( dir_name );
    if( NULL == pDir )
    {
        fprintf(stderr, "can not open dir %s.", dir_name);
        return -1;
    }

    char fileEntryPathBuf[PATH_MAX] = { 0 };

    /* loop all  */
    while( ( pDirEntry = readdir(pDir) ) != NULL )
    {
        if( strcmp( pDirEntry->d_name , "." ) == 0 ||
            strcmp( pDirEntry->d_name , "..") == 0    )
        {   // 忽略它俩
            continue;
        }

        /* 只处理目录和常规文件 */
        if (pDirEntry->d_type != DT_DIR
            && pDirEntry->d_type != DT_REG)
        {
            continue;
        }

        /* 生成 fileEntry path */
        sprintf(fileEntryPathBuf, "%s/%s", dir_name, pDirEntry->d_name);
        func(fileEntryPathBuf, pDirEntry->d_type == DT_DIR);
    }

    closedir(pDir);
    return 0;
}

static inline
int makeDir(const char* path)
{
    int beginCmpPath;
    int endCmpPath;
    int pathLen = strlen(path);
    char currentPath[PATH_MAX] = {0};

    //相对路径
    if('/' != path[0])
    {
        //获取当前路径
        getcwd(currentPath, sizeof(currentPath));
        strcat(currentPath, "/");
        beginCmpPath = strlen(currentPath);
        strcat(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        endCmpPath = strlen(currentPath);
    }
    else
    {
        //绝对路径
        int pathLen = strlen(path);
        strcpy(currentPath, path);
        if(path[pathLen] != '/')
        {
            strcat(currentPath, "/");
        }
        beginCmpPath = 1;
        endCmpPath = strlen(currentPath);
    }
    //创建各级目录
    for(int i = beginCmpPath; i < endCmpPath ; i++ )
    {
        if('/' == currentPath[i])
        {
            currentPath[i] = '\0';
            if(access(currentPath, 0) != 0)
            {
                if(mkdir(currentPath, 0755) == -1)
                {
                    perror("mkdir error %s\n");
                    return -1;
                }
            }
            currentPath[i] = '/';
        }
    }
    return 0;
}

/* return dir string with final backslash */
static inline
std::string getAppDir()
{
    char pathBuff[PATH_MAX] = { 0 };
    uint cnt = readlink("/proc/self/exe", pathBuff, sizeof(pathBuff));
    if (cnt >= sizeof(pathBuff))
    {
        return "";
    }

    std::string strAppPath(pathBuff);
    return strAppPath.substr(0, strAppPath.find_last_of('/') + 1);
}

#endif /* _COMM_TOOLS_H_ */
