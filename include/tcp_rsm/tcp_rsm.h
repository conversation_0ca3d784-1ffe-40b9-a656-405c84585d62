/****************************************************************************************
 * 设    计 : Powered By chunli.
*****************************************************************************************/
#ifndef _TCP_RSM_DESIGN_BY_CHUNLI_H_
#define _TCP_RSM_DESIGN_BY_CHUNLI_H_

#include <sys/types.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

enum TCP_RSM_FLAG
{
    TCP_FIN     =   1 << 0,
    TCP_SYN     =   1 << 1,
    TCP_RST     =   1 << 2,
    TCP_PSH     =   1 << 3,
    TCP_ACK     =   1 << 4,
    TCP_POP     =   1 << 5, //[重组标记]
};

struct tcp_rsm_t;
typedef int  (*fn_dequeue) (void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
typedef int  (*fn_miss)(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);

//功能接口: tcp_rsm_push() 会将 cb_dequeue()/cb_miss()的返回值 直接 传递给应用层
struct tcp_rsm_t* tcp_rsm_init (fn_dequeue cb_dequeue, fn_miss cb_miss, void *user, uint32_t window_size);
int               tcp_rsm_push (struct tcp_rsm_t *rsm, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
void              tcp_rsm_free (struct tcp_rsm_t *rsm);

#ifdef __cplusplus
}
#endif

#endif
