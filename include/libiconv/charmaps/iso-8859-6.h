static const unsigned char map_iso_8859_6[] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x81, 0x00, 0x82, 0x00, 0x83,
	0x00, 0x84, 0x00, 0x85, 0x00, 0x86, 0x00, 0x87, 0x00, 0x88, 0x00, 0x89,
	0x00, 0x8a, 0x00, 0x8b, 0x00, 0x8c, 0x00, 0x8d, 0x00, 0x8e, 0x00, 0x8f,
	0x00, 0x90, 0x00, 0x91, 0x00, 0x92, 0x00, 0x93, 0x00, 0x94, 0x00, 0x95,
	0x00, 0x96, 0x00, 0x97, 0x00, 0x98, 0x00, 0x99, 0x00, 0x9a, 0x00, 0x9b,
	0x00, 0x9c, 0x00, 0x9d, 0x00, 0x9e, 0x00, 0x9f, 0x00, 0xa0, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x00, 0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06, 0x0c, 0x00, 0xad,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x06, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06, 0x1f,
	0xff, 0xff, 0x06, 0x21, 0x06, 0x22, 0x06, 0x23, 0x06, 0x24, 0x06, 0x25,
	0x06, 0x26, 0x06, 0x27, 0x06, 0x28, 0x06, 0x29, 0x06, 0x2a, 0x06, 0x2b,
	0x06, 0x2c, 0x06, 0x2d, 0x06, 0x2e, 0x06, 0x2f, 0x06, 0x30, 0x06, 0x31,
	0x06, 0x32, 0x06, 0x33, 0x06, 0x34, 0x06, 0x35, 0x06, 0x36, 0x06, 0x37,
	0x06, 0x38, 0x06, 0x39, 0x06, 0x3a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0x06, 0x40, 0x06, 0x41, 0x06, 0x42, 0x06, 0x43,
	0x06, 0x44, 0x06, 0x45, 0x06, 0x46, 0x06, 0x47, 0x06, 0x48, 0x06, 0x49,
	0x06, 0x4a, 0x06, 0x4b, 0x06, 0x4c, 0x06, 0x4d, 0x06, 0x4e, 0x06, 0x4f,
	0x06, 0x50, 0x06, 0x51, 0x06, 0x52, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff
};
