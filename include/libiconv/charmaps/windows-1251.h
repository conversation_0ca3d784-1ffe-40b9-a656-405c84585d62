static const unsigned char map_windows_1251[] = {
	0x00, 0x00, 0x00, 0x00, 0x04, 0x02, 0x04, 0x03, 0x20, 0x1a, 0x04, 0x53,
	0x20, 0x1e, 0x20, 0x26, 0x20, 0x20, 0x20, 0x21, 0x20, 0xac, 0x20, 0x30,
	0x04, 0x09, 0x20, 0x39, 0x04, 0x0a, 0x04, 0x0c, 0x04, 0x0b, 0x04, 0x0f,
	0x04, 0x52, 0x20, 0x18, 0x20, 0x19, 0x20, 0x1c, 0x20, 0x1d, 0x20, 0x22,
	0x20, 0x13, 0x20, 0x14, 0xff, 0xff, 0x21, 0x22, 0x04, 0x59, 0x20, 0x3a,
	0x04, 0x5a, 0x04, 0x5c, 0x04, 0x5b, 0x04, 0x5f, 0x00, 0xa0, 0x04, 0x0e,
	0x04, 0x5e, 0x04, 0x08, 0x00, 0xa4, 0x04, 0x90, 0x00, 0xa6, 0x00, 0xa7,
	0x04, 0x01, 0x00, 0xa9, 0x04, 0x04, 0x00, 0xab, 0x00, 0xac, 0x00, 0xad,
	0x00, 0xae, 0x04, 0x07, 0x00, 0xb0, 0x00, 0xb1, 0x04, 0x06, 0x04, 0x56,
	0x04, 0x91, 0x00, 0xb5, 0x00, 0xb6, 0x00, 0xb7, 0x04, 0x51, 0x21, 0x16,
	0x04, 0x54, 0x00, 0xbb, 0x04, 0x58, 0x04, 0x05, 0x04, 0x55, 0x04, 0x57,
	0x04, 0x10, 0x04, 0x11, 0x04, 0x12, 0x04, 0x13, 0x04, 0x14, 0x04, 0x15,
	0x04, 0x16, 0x04, 0x17, 0x04, 0x18, 0x04, 0x19, 0x04, 0x1a, 0x04, 0x1b,
	0x04, 0x1c, 0x04, 0x1d, 0x04, 0x1e, 0x04, 0x1f, 0x04, 0x20, 0x04, 0x21,
	0x04, 0x22, 0x04, 0x23, 0x04, 0x24, 0x04, 0x25, 0x04, 0x26, 0x04, 0x27,
	0x04, 0x28, 0x04, 0x29, 0x04, 0x2a, 0x04, 0x2b, 0x04, 0x2c, 0x04, 0x2d,
	0x04, 0x2e, 0x04, 0x2f, 0x04, 0x30, 0x04, 0x31, 0x04, 0x32, 0x04, 0x33,
	0x04, 0x34, 0x04, 0x35, 0x04, 0x36, 0x04, 0x37, 0x04, 0x38, 0x04, 0x39,
	0x04, 0x3a, 0x04, 0x3b, 0x04, 0x3c, 0x04, 0x3d, 0x04, 0x3e, 0x04, 0x3f,
	0x04, 0x40, 0x04, 0x41, 0x04, 0x42, 0x04, 0x43, 0x04, 0x44, 0x04, 0x45,
	0x04, 0x46, 0x04, 0x47, 0x04, 0x48, 0x04, 0x49, 0x04, 0x4a, 0x04, 0x4b,
	0x04, 0x4c, 0x04, 0x4d, 0x04, 0x4e, 0x04, 0x4f
};
