static const unsigned char map_koi8_r[] = {
	0x00, 0x00, 0x00, 0x00, 0x25, 0x00, 0x25, 0x02, 0x25, 0x0c, 0x25, 0x10,
	0x25, 0x14, 0x25, 0x18, 0x25, 0x1c, 0x25, 0x24, 0x25, 0x2c, 0x25, 0x34,
	0x25, 0x3c, 0x25, 0x80, 0x25, 0x84, 0x25, 0x88, 0x25, 0x8c, 0x25, 0x90,
	0x25, 0x91, 0x25, 0x92, 0x25, 0x93, 0x23, 0x20, 0x25, 0xa0, 0x22, 0x19,
	0x22, 0x1a, 0x22, 0x48, 0x22, 0x64, 0x22, 0x65, 0x00, 0xa0, 0x23, 0x21,
	0x00, 0xb0, 0x00, 0xb2, 0x00, 0xb7, 0x00, 0xf7, 0x25, 0x50, 0x25, 0x51,
	0x25, 0x52, 0x04, 0x51, 0x25, 0x53, 0x25, 0x54, 0x25, 0x55, 0x25, 0x56,
	0x25, 0x57, 0x25, 0x58, 0x25, 0x59, 0x25, 0x5a, 0x25, 0x5b, 0x25, 0x5c,
	0x25, 0x5d, 0x25, 0x5e, 0x25, 0x5f, 0x25, 0x60, 0x25, 0x61, 0x04, 0x01,
	0x25, 0x62, 0x25, 0x63, 0x25, 0x64, 0x25, 0x65, 0x25, 0x66, 0x25, 0x67,
	0x25, 0x68, 0x25, 0x69, 0x25, 0x6a, 0x25, 0x6b, 0x25, 0x6c, 0x00, 0xa9,
	0x04, 0x4e, 0x04, 0x30, 0x04, 0x31, 0x04, 0x46, 0x04, 0x34, 0x04, 0x35,
	0x04, 0x44, 0x04, 0x33, 0x04, 0x45, 0x04, 0x38, 0x04, 0x39, 0x04, 0x3a,
	0x04, 0x3b, 0x04, 0x3c, 0x04, 0x3d, 0x04, 0x3e, 0x04, 0x3f, 0x04, 0x4f,
	0x04, 0x40, 0x04, 0x41, 0x04, 0x42, 0x04, 0x43, 0x04, 0x36, 0x04, 0x32,
	0x04, 0x4c, 0x04, 0x4b, 0x04, 0x37, 0x04, 0x48, 0x04, 0x4d, 0x04, 0x49,
	0x04, 0x47, 0x04, 0x4a, 0x04, 0x2e, 0x04, 0x10, 0x04, 0x11, 0x04, 0x26,
	0x04, 0x14, 0x04, 0x15, 0x04, 0x24, 0x04, 0x13, 0x04, 0x25, 0x04, 0x18,
	0x04, 0x19, 0x04, 0x1a, 0x04, 0x1b, 0x04, 0x1c, 0x04, 0x1d, 0x04, 0x1e,
	0x04, 0x1f, 0x04, 0x2f, 0x04, 0x20, 0x04, 0x21, 0x04, 0x22, 0x04, 0x23,
	0x04, 0x16, 0x04, 0x12, 0x04, 0x2c, 0x04, 0x2b, 0x04, 0x17, 0x04, 0x28,
	0x04, 0x2d, 0x04, 0x29, 0x04, 0x27, 0x04, 0x2a
};
