# surveillance camera项目

---

## 构建

---

### 在amd64平台下编译方法

编译方法

```
mkdir -p build ; cmake3 -B build && make -j -C build
```

注：log模块需要使用gcc 9版本才能编译成功

启用方法

```
yumi install devtoolset-9
scl enable devtoolset-9 bash
```

### 在Ubuntu 环境下交叉编译ARMv7-OpenWrt环境可运行的程序

将程序目录拷贝到OpenWrt源码目录下的package目录下

目录结构为

```makefile
> .
> ├── Src_NorFlash
> │   ├── package
>            ├── libipc
>                  ├── Makefile
>                  └── src
```

其中 MakeFile的格式为


```
#
# Copyright (C) 2006-2016 OpenWrt.org
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

include $(TOPDIR)/rules.mk

PKG_NAME:=libipc
PKG_VERSION:=1.0
PKG_RELEASE:=1


# PKG_BUILD_PARALLEL:=1
# PKG_INSTALL:=1

include $(INCLUDE_DIR)/package.mk
include $(INCLUDE_DIR)/cmake.mk
define Package/libipc
	SECTION:=Utilities
	TITLE:=libipc
	CATEGORY:=Utilities
	DEPENDS:=+!USE_MUSL:libthread-db +libstdcpp +libpcap +glib2 +libiconv
endef

define Package/libipc/description
libipc libipc
endef

define Package/libipc/install
	$(INSTALL_DIR) $(1)/dev/shm/libipc
	$(CP) $(PKG_BUILD_DIR)/run/* $(1)/dev/shm/libipc/

	$(INSTALL_DIR) $(1)/$(INSTALL_BINDIR)
	$(CP) $(PKG_BUILD_DIR)/run/* $(1)/$(INSTALL_BINDIR)/
endef




CXX_FLAGS+= \
	    -O0 -g

CXXFLAGS+= -g

CONFIGURE_VARS+= \
	ac_cv_search_tgetent="$(TARGET_LDFLAGS) -lncurses -lreadline"

TARGET_LDFLAGS+= \
	$(INTL_LDFLAGS) $(if $(INTL_FULL),-lintl) \
	-static-libstdc++ \

$(eval $(call BuildPackage,libipc))


```


src目录内为真正的工程目录，目录结构为
```
> .
> ├── Src_NorFlash
> │   ├── package
>            ├── libipc
>                  ├── Makefile
>                  └── src
>                       ├── build
>                       ├── cmake
>                       ├── etc
>                       ├── include
>                       ├── lib
>                       ├── run
>                       ├── src
>                       └── CMakeLists.txt
```
`注：在arm下编译，需要将lib/arm/目录下的库文件替换到lib目录下`
编译命令：
```
make menuconfig
```
出现图形化界面，在图形化界面中选中yaslc工程（Utilities的选项中，可配置在哪个目录中，makefile中配置）

勾选为 [*] libipc
按exit两次退出,默认保存为.config
在openwrt源码根目录下输入

```
rm build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0/* -rf&& make package/libipc/compile V=s
```
单独编译yaslc工程
`/compile`的含义为 编译yaslc/目录下的compile target 含义等同于在build目录中 make compile
此时，openwrt的make工具会将代码拷贝进入arm环境下的路径 build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0目录中进行自动编译

编译出的二进制文件也会生成在该目录下

##arm中调试方法
安上述编译步骤，勾选devel中的gdb，编译gdb
```
make package/devel/gdb/compile V=s
```
gdb中包含gdb与gdbserver两个可执行文件

在`build_dir/target-arm_cortex-a7_musl_eabi/gdb-10.1/gdbserver/`目录下找到gdbserver二进制可执行文件，拷贝到arm板上，执行
```
./gdbserver 127.0.0.1:3000 VideoEncode
```
启动gdbserver

在编译机opwrt源码目录下`staging_dir/toochain-arm_cortex-a7_gcc-8.4.0_musl_eabi/bin`
中找到`arm-openwrt-linux-gdb`可执行文件，运行
```
./arm-openwrt-linux-gdb
target remote 开发板ip:端口
```
可以远程连接开发板进行调试
gdbserver会默认运行到入口函数暂停
执行
```
c
```
启动程序，其余操作与amd64平台相同

#### 将编译出的可执行文件剥离符号

```
staging_dir/toolchain-arm_cortex-a7_gcc-8.4.0_musl_eabi/bin/arm-openwrt-linux-strip build_dir/target-arm_cortex-a7_musl_eabi/libipc-1.0/run/yaslc_0.0.1
```

### 配置开发板开机启动程序
1 将release版本程序与配置文件拷贝到开发板root目录下
2 配置`/etc/rc.local`文件
```
# Put your custom commands here that should be executed once
# the system init finished. By default this file does nothing.

echo 3 > /proc/sys/vm/drop_caches

if [ -e /sbin/boot_times.sh ]; then
        /sbin/boot_times.sh  &
fi
sleep 10
echo starting >> /root/start.log
/bin/date >> /root/start.log 
# 配置网卡镜像操作
ip link set up dev lan1
tc qdisc add dev lan2 clsact
tc filter add dev lan2 ingress matchall skip_sw action mirred egress mirror dev lan1
tc filter add dev lan2 egress matchall skip_sw action mirred egress mirror dev lan1
ethtool -K "eth0" gso off gro off tso off
# 启动程序
/root/yaslc_0.0.1

echo starting_finish >> /root/start.log
/bin/date >> /root/start.log 
exit 0
```

## 设计
version 0.0 ： 通过main程序启动 

主线程
----------
在程序启动时 协议解析器注册到 [g_RegDissectCallbackMap] 中
只实现了Rtp协议
构造抓包类
启动发送线程


解析线程
----------
  libpcap读取网卡流量 或 读取离线pcap文件 [SlcCapture] 类集成libpcap

  [SLCPARSER] 类 解析到ip层 通过注册的协议解析器 遍历识别

  当识别成功 进入解析过程
  [RtpKeeper] 类 自身管理一个 [map_streams_] 的表
  表结构为
  ```
  std::map<flow_info *, std::shared_ptr<RtpStream>>
  ```
  通过flow_info * 直接将rtp的payload送入 [RtpStream]类

  [RtpStream]中维护自己的[Unpacker_] (设计缺陷 只支持h264)
  [RtpStream]中还存在一个存放NALU结构的队列类[SlcNaluqueue]

  [SlcNaluqueue]可以通过接口入队与出队


  [Unpacker_]为类型为 [RtpH264Unpacker] 的类
  [Unpacker_]注册了[SlcNaluqueue]入队的回调接口

  [RtpStream]直接将rtp的payload转送给 [RtpH264Unpacker]

  当 [RtpH264Unpacker]解析完一帧完整的NALU结构时 调用回调函数
  [NaluCallback] 将nalu结构存入[RtpStream].[SlcNaluqueue]

发送线程
----------------
[SendFrameThread] 线程从 [RtpKeeper].[map_streams_] 中遍历流 然后通过 [RtpStream].[SlcNaluqueue]的出队接口，不断出队
出队后 通过[RtpH264Packer]打包成完整的rtp，在进行发送

version 0.1 ： 添加了RTSP服务器

主线程
----------
构造rtsp服务器

发送线程
----------------

当收到rtsp获取视频流时 才开始发送


version 0.2 ： 添加以接口形式

将程序抽象成以下的层级

接口层

[ipc_config]
[ipc_decoder]

公共解析层(框架层)

[SLCPARSER]

协议解析层

[RTPKEEPER]
[RTSPKEEPER]
[DHAVKEEPER]

解码层

[h264Unpacker]
----------------

接口层需要实现 送入网络流量 通过回调函数的方式将nalu返回给注册的函数



 
### 增加新协议需要完成的接口
src/src/proto目录为存放协议解析文件
以slc_dhav为例子：
定义一个协议解析类（单例），用于管理协议流（proto_stream）
解析类
slc_dhav.h
```cpp
#define DHAVKEEPER DHAVKeeper::GetInstance()
//此处的继承都是需要的，用于进行协议管理
class DHAVKeeper : public ObserverSessionKeeper, public singleton<DHAVKeeper> {
  friend class SlcProtoLoader<DHAVKeeper>;

public:
  DHAVKeeper();//需要在构造中调用回调注册函数将自身注册到协议初始化中
  void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);//必须实现,协议识别函数


  //挑选一个进行具体实现，必须将两个函数都进行绑定，在绑定时可以绑定NULL
  //tcp重组发生在框架层，dissectProto_rsm到达的报文一定是tcp有序报文
  void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);//不需要tcp重组的解析函数
  int  dissectProto_rsm(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);//需要tcp重组的解析函数
  int  miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);      //需要重组的丢包通知

  std::map<flow_info *, std::shared_ptr<DHAVStream>> map_streams_;//管理流的map
};
```

slc_dhav.cpp
```cpp
auto dhavkeeper = DHAVKEEPER;
DHAVKeeper::DHAVKeeper() {
  setRegCallback(IPC_PROTOCOL_DHAV,"dhav",
      //识别函数 必须
      std::bind(&DHAVKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      //不需要重组的解析函数
      std::bind(&DHAVKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      //需要重组的解析函数
      std::bind(&DHAVKeeper::dissectProto_rsm, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4, std::placeholders::_5, std::placeholders::_6, std::placeholders::_7),
      //需要重组的丢包通知
      std::bind(
          &DHAVKeeper::miss, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
}
```

    附：可以绑定空解析函数的解析类
    ```cpp
      RtpKeeper::RtpKeeper() {
        setRegCallback(IPC_PROTOCOL_RTP, "rtp",
            //识别函数 必须
            std::bind(&RtpKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
                std::placeholders::_4),
            //不需要重组的解析函数
            std::bind(&RtpKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
                std::placeholders::_4),
            //需要重组的解析函数 空
            NULL,
            //需要重组的丢包通知 空
            NULL);
      }
    ```


协议stream类
此类为协议解析的实现
```cpp
class DHAVStream : flow_info {
public:
}
```
继承flow_info是为了方便传递五元组


在协议stream类中调用 
```cpp
  flow_info *stream = (flow_info *)(this);
  if (stream->user) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(stream->getUserData());

    decoder->onNaluCallback(naluType, nalu, naluLen, (void *)stream);
  } 
```
来实现解码后的视频流送回slc_capture.cpp:128的发送到rtsp客户端

在现有的设计中，协议解析部分是放在stream类中实现的 ，在 `协议解析类` 中实现也是可以的 


