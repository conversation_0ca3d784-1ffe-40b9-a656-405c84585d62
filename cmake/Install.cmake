message("===== install cmake =====")

install(
  TARGETS 
    ${AppName}
    ${YA_PROJECT_LIBRARY}
  RUNTIME DESTINATION
    ${INSTALL_BINDIR}
)
INSTALL(TARGETS ${AppName} RUNTIME DESTINATION /dev/shm/${AppName}/)

if (slc_files STREQUAL "")
  message("empty after slc_files = ${slc_files}")
else()
  install(
    FILES 
      ${slc_files}
    DESTINATION
      ${INSTALL_ROOTDIR}
  )
  message("after slc_files = ${slc_files}")
  endif()
  
  
if (slc_dirs STREQUAL "")
  message("empty after slc_dirs = ${slc_dirs}")
else()
  install(
    DIRECTORY  
      ${slc_dirs}
    DESTINATION
      ${INSTALL_ROOTDIR}
  )
  message("after slc_dirs = ${slc_dirs}")
endif()

if (slc_bins STREQUAL "")
  message("empty after slc_bins = ${slc_bins}")
else()
  install(
    PROGRAMS  
      ${slc_bins}
    DESTINATION
      ${INSTALL_ROOTDIR}
  )
  message("after slc_bins = ${slc_bins}")
endif()
