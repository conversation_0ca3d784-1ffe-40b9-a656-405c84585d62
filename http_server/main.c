#include "mongoose/mongoose.h"
#include "cjson/cJSON.h"
#include <signal.h>
#include <stdlib.h>
#include <stdio.h>
#include <iniparser/iniparser.h>
#include <pthread.h>

#ifndef PATH_MAX
#define PATH_MAX    4096
#endif

#define MAIN_VERSION_MAJOR 0
#define MAIN_VERSION_MINOR 0
#define MAIN_VERSION_PATCH 1

static uint16_t listen_port = 8051;

static int should_stop = 0;
void sig_handle(int sig_no)
{
    should_stop = 1;
}

extern const char *guess_content_type(const char *filename);


/** dpi 接口 **/
#if 1
struct dpi
{
    char    name[128];
    char    root_dir[1024];
    char    capture_file[PATH_MAX];
    char    stats_file[PATH_MAX];
    char    plugin_sylink_file[PATH_MAX];   // 插件的软链接文件
    char    last_plugin_file[PATH_MAX];     // 最新使用的插件文件

    char    pcaps_dump_dir[1024];

    uint8_t capturing;
    pid_t   pid;
    const char *log_level;
};

static struct dpi *global_dpi;

pthread_mutex_t dpi_mutex = PTHREAD_MUTEX_INITIALIZER;

/**
 *  正在运行返回 pid, 否则返回 0; 
 */
int dpi_is_running(struct dpi *dpi)
{
    LOG(LL_DEBUG, (__func__));

    char command[256];
    snprintf(command, sizeof(command), "pgrep %s", dpi->name);    
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        return 0;
    }

    char result[16];
    int found = fgets(result, sizeof(result), fp) != NULL;
    pclose(fp);

    return found ? strtold(result, NULL) : 0;
}

int dpi_opt_start(struct dpi *dpi)
{
    LOG(LL_DEBUG, (__func__));

    char command[PATH_MAX];
    snprintf(command, sizeof(command), "%s/start.sh", dpi->root_dir);

    system(command);
    mg_usleep(100 * 1000);
    return 0;


    pid_t pid = fork();

    if (pid == 0) {
        system(command);
        exit(0);
    } else if (pid > 0) {
        dpi->pid = pid;
    } else {
        LOG(LL_ERROR, ("fork failed: %s", strerror(errno)));
        return -1;
    }

    return 0;
}

int dpi_opt_stop(struct dpi *dpi)
{
    LOG(LL_DEBUG, ("dpi_opt_stop"));

    // if (dpi->pid == 0)
    //     return 0;

    pid_t pid;

    while ((pid=dpi_is_running(dpi)) > 0) {
        kill(pid, SIGKILL);
        LOG(LL_INFO, ("等待 dpi 运行结束"));
        sleep(1);
    }

    dpi->pid = 0;

    return 0;
}


int dpi_opt_capture_start(struct dpi *dpi)
{
    LOG(LL_DEBUG, (__func__));

    int ret = kill(dpi->pid, SIGUSR1);

    if (ret == -1) {
        LOG(LL_ERROR, ("kill %d SIGUSR1 failed", dpi->pid));
    }

    dpi->capturing = 1;

    return ret;
}

int dpi_opt_capture_stop(struct dpi *dpi)
{
    LOG(LL_DEBUG, (__func__));

    int ret = kill(dpi->pid, SIGUSR2);

    if (ret == -1) {
        LOG(LL_ERROR, ("kill %d SIGUSR2 failed", dpi->pid));
    }

    dpi->capturing = 0;

    return ret;
}

int dpi_reload_plugin(struct dpi *dpi, const char *plugin)
{
    return 0;
}

int dpi_get_status(struct dpi *dpi)
{
    return 0;
}

#endif


/** utils **/
#if 2

int check_plugin_vaild(const char *buff, size_t len)
{
#if 0
    static const char elf_magic[] = {
        0x7f, 0x45, 0x4c, 0x46, 
        0x02, 0x01, 0x01, 0x03,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00
    };
#else
    static const char elf_magic[] = {
        0x7f, 0x45, 0x4c, 0x46, 
        0x02, 0x01, 0x01
    };
#endif

    if (len < sizeof(elf_magic))
        return 1;

    return memcmp(buff, elf_magic, sizeof(elf_magic));
}


char* time_format(time_t *t, char *buffout, size_t sz)
{
    strftime(buffout, sz, "%Y-%m-%d %H:%M:%S", localtime(t));

    return buffout;
}

void mkdirs(const char *dir)
{
    if(dir==NULL || access(dir,0)==0)
    {
        return;
    }

    char tmp[2048];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp),"%s",dir);
    len = strlen(tmp);
    if(tmp[len - 1] == '/')
        tmp[len - 1] = 0;
    for(p = tmp + 1; *p; p++)
        if(*p == '/') {
            *p = 0;
            mkdir(tmp, 0777);
            *p = '/';
        }
    mkdir(tmp, 0777);
}


static inline void
print_request_log(struct mg_connection *c, struct mg_http_message *hm)
{
    char ip[40];
    mg_ntoa(&c->peer, ip, 40);

    char timebuf[21];
    time_t t = time(NULL);
    struct tm *tm = localtime(&t);
    strftime(timebuf, sizeof(timebuf), "%Y-%m-%d %H:%M:%S", tm);

    fprintf(stdout, "%s  \033[33m%15s \"%.*s\"\033[0m %lu\n",
        timebuf, ip,
        (int)(hm->proto.ptr+hm->proto.len - hm->method.ptr), hm->method.ptr,
        hm->body.len);
}


const char* main_version(void)
{
    static char version[15] = {0};

    if (version[0] == '\0') {
        sprintf(version, "%i.%i.%i", MAIN_VERSION_MAJOR, MAIN_VERSION_MINOR, MAIN_VERSION_PATCH);
    }

    return version;
}

#endif


/** RESTful接口 **/
#if 3
static const struct mg_str  method_get = {"GET", 3};
static const struct mg_str  method_post = {"POST", 4};
static const struct mg_str  method_delete = {"DELETE", 6};
static const struct mg_str  method_options = {"OPTIONS", 7};

static const char *public_res_headers = \
    "Access-Control-Allow-Origin: *\r\n"\
    "Access-Control-Allow-Methods: 'GET, POST, PUT, DELETE, OPTIONS'\r\n"\
    "Access-Control-Allow-Headers: *\r\n";

static const char *public_res_headers_with_josn = \
    "Access-Control-Allow-Origin: *\r\n"\
    "Access-Control-Allow-Methods: 'GET, POST, PUT, DELETE, OPTIONS'\r\n"\
    "Access-Control-Allow-Headers: *\r\n"\
    "Content-Type: application/json\r\n";


static inline
void http_403_response(struct mg_connection *c)
{
    mg_http_reply(c, 403, public_res_headers, "Forbidden");
}

static inline
void http_404_response(struct mg_connection *c, struct mg_str uri)
{
    mg_http_reply(c, 404, public_res_headers, "Not Found %.*s", uri.len, uri.ptr);
}

static inline
void http_405_response(struct mg_connection *c)
{
    mg_http_reply(c, 405, public_res_headers, "Method Not Allowed");
}

static inline
void http_error_response(struct mg_connection *c, int code, char *msg)
{
    mg_http_reply(c, 200, public_res_headers_with_josn, "{\"code\": %d, \"msg\": \"%s\"}", code, msg);
}

static inline
void http_success_response(struct mg_connection *c)
{
    mg_http_reply(c, 200, public_res_headers_with_josn, "{\"code\": 0, \"msg\": \"ok\"}");
}


/** 1.1 状态查询 **/
void dpi_handle(struct mg_connection *c, struct mg_http_message *hm)
{
    if (mg_http_match_uri(hm, "/api/sxt/dpi/status")) {
        if (mg_strcmp(hm->method, method_get) == 0) {

            static char ip[64] = {'\0'};
            if (ip[0] == '\0') {
                struct mg_str *host = mg_http_get_header(hm, "host");
                snprintf(ip, sizeof(ip), "%.*s", (int)host->len, host->ptr);
                char *p = strchr(ip, ':');
                if (p) *p='\0';
            }
          
            LOG(LL_DEBUG, ("req ip: %s\n", ip));

            cJSON *res = cJSON_CreateObject();
            dictionary *dict = NULL;
            bool run = dpi_is_running(global_dpi) != 0;

            if (!run) {
                cJSON_AddNumberToObject(res, "code", 1);
                cJSON_AddStringToObject(res, "msg", "dpi not running");
            } else if ((dict = iniparser_load(global_dpi->stats_file)) == NULL) {
                cJSON_AddNumberToObject(res, "code", 2);
                cJSON_AddStringToObject(res, "msg", "failed parse file: status");
                LOG(LL_ERROR, ("failed parse file: status"));
            } else {
                cJSON_AddNumberToObject(res, "code", 0);
                cJSON_AddStringToObject(res, "msg", "ok");

                cJSON *data = cJSON_AddObjectToObject(res, "data");
                cJSON_AddNumberToObject(data, "running", 1);
                cJSON_AddNumberToObject(data, "protocol_type", atoi(iniparser_getstring(dict, ":PROTOCOL_TYP", "0")));
                cJSON_AddNumberToObject(data, "yaslc_status", atoi(iniparser_getstring(dict, ":YASLC_STATUS", "0")));

                char rtsp_addr[128] = {0};
                snprintf(rtsp_addr, sizeof(rtsp_addr), "rtsp://%s:%s",
                            ip[0] ? ip : "127.0.0.1",
                            iniparser_getstring(dict, ":RTSP_ADDR", ""));
                cJSON_AddStringToObject(data, "rtsp_addr", rtsp_addr);

                // capture 信息
                cJSON_AddNumberToObject(data, "capturing", global_dpi->capturing);
                dictionary *capture_dict = iniparser_load(global_dpi->capture_file);
                if (capture_dict) {
                    const char *str = iniparser_getstring(capture_dict, ":brand", NULL);
                    if (str) cJSON_AddStringToObject(data, "brand", str);
                    else cJSON_AddNullToObject(data, "brand");

                    str = iniparser_getstring(capture_dict, ":model", NULL);
                    if (str) cJSON_AddStringToObject(data, "model", str);
                    else cJSON_AddNullToObject(data, "model");

                    str = iniparser_getstring(capture_dict, ":ORB", NULL);
                    if (str) cJSON_AddStringToObject(data, "ORB", str);
                    else cJSON_AddNullToObject(data, "ORB");

                    iniparser_freedict(capture_dict);
                } else {
                    cJSON_AddNullToObject(data, "brand");
                    cJSON_AddNullToObject(data, "model");
                    cJSON_AddNullToObject(data, "ORB");
                }
            }

            char *res_str = cJSON_PrintUnformatted(res);

            mg_http_reply(c, 200, public_res_headers_with_josn, res_str);

            free(res_str);
            cJSON_Delete(res);
            if (dict) iniparser_freedict(dict);

        } else {
            http_405_response(c);
        }
    } else {
        http_404_response(c, hm->uri);
    }
}

/** 1.2 抓包管理 **/

void capture_handle(struct mg_connection *c, struct mg_http_message *hm)
{
    LOG(LL_DEBUG, (__func__));

    if (!mg_strcmp(hm->method, method_post)) {
        char var[64] = {0};
        if (mg_http_get_var(&hm->query, "stop", var, sizeof(var)) < 0) {
            // 开始抓包

            printf("%.*s\n", 1024, hm->message.ptr);

            #define VAR_VALUE_LENGTH 64
            struct {
                const char *name;
                char value[VAR_VALUE_LENGTH];
            } param_list[] = {
                {.name="brand", .value=""},
                {.name="model", .value=""},
                {.name="ORB", .value=""},
                {.name="filesize", .value=""},
            };

            struct mg_str *content_type = mg_http_get_header(hm, "content-type");

            int i;
            
            if (content_type && !mg_vcasecmp(content_type, "application/json")) {

                cJSON *root = cJSON_ParseWithLength(hm->body.ptr, hm->body.len);
                if (root == NULL) {
                    http_error_response(c, __LINE__, "json parser error");
                    return;
                }

                // 前 3 个是必选字段
                cJSON *item;
                for (i=0; i < 3; i++)
                {
                    item = cJSON_GetObjectItem(root, param_list[i].name);
                    if (item == NULL || item->valuestring == NULL) {
                        mg_http_reply(c, 200, public_res_headers_with_josn,
                                "{\"code\": 1, \"msg\": \"缺少参数 '%s'\"}", param_list[i].name);
                        cJSON_Delete(root);
                        return;
                    }
                    strncpy(param_list[i].value, item->valuestring, VAR_VALUE_LENGTH);
                }
                
                // 第4个起是可选字段
                for (; i < (sizeof(param_list)/sizeof(param_list[0])); i++)
                {
                    item = cJSON_GetObjectItem(root, param_list[i].name);
                    if (item && item->valuestring) {
                        strncpy(param_list[i].value, item->valuestring, VAR_VALUE_LENGTH);
                    }
                }

                item = cJSON_GetObjectItem(root, "file_path");
                if (item && item->valuestring) {
                    strncpy(global_dpi->pcaps_dump_dir, item->valuestring, sizeof(global_dpi->pcaps_dump_dir));
                }

                cJSON_Delete(root);

            } else {
                // 前 3 个是必选字段
                for (i=0; i < 3; i++)
                {
                    if (mg_http_get_var(&hm->body, param_list[i].name, param_list[i].value, VAR_VALUE_LENGTH) <= 0)
                    {
                        mg_http_reply(c, 200, public_res_headers_with_josn,
                                "{\"code\": 1, \"msg\": \"缺少参数 '%s'\"}", param_list[i].name);
                        return;
                    }
                }

                // 第4个起是可选字段
                for (; i < (sizeof(param_list)/sizeof(param_list[0])); i++)
                {
                    mg_http_get_var(&hm->body, param_list[i].name, param_list[i].value, VAR_VALUE_LENGTH);
                }

                if (mg_http_get_var(&hm->body, "file_path", global_dpi->pcaps_dump_dir,
                        sizeof(global_dpi->pcaps_dump_dir)) < 0) {
                    global_dpi->pcaps_dump_dir[0] = '/';
                }
            }
        
            int j;
            for (j=0; j<strlen(param_list[3].value); j++) {
                if (!isdigit(param_list[3].value[j])) {
                    param_list[3].value[j] = '\0';
                    break;
                }
            }

            FILE *fp = fopen(global_dpi->capture_file, "w");
            for (i=0; i<(sizeof(param_list)/sizeof(param_list[0])); i++) {
                fprintf(fp, "%s = %s\n", param_list[i].name, param_list[i].value);
            }
            fprintf(fp, "%s = %s\n", "file_path", global_dpi->pcaps_dump_dir);

            // 传递参数
            // dictionary *dict = dictionary_new(6);
            // dictionary_set(dict, ":BRAND", param_list[0].value);
            // dictionary_set(dict, ":MODEL", param_list[1].value);
            // dictionary_set(dict, ":ORB",   param_list[2].value);
            // dictionary_set(dict, ":CAP_MAX_FILESIZE", param_list[3].value[0] ? param_list[0].value : "100");
            // dictionary_set(dict, ":FILE_PATH", "");
            // dictionary_dump(dict, fp);
            // dictionary_del(dict);

            fclose(fp);

            if (global_dpi->pid == 0) {
                http_error_response(c, __LINE__, "dpi 程序没有运行");
                return;
            }

            dpi_opt_capture_start(global_dpi);

            http_success_response(c);
        }
        else if(!strcmp(var, "1")) {
            // 停止抓包
            if (global_dpi->pid == 0) {
                http_error_response(c, __LINE__, "dpi 程序没有运行");
                return;
            }
    
            dpi_opt_capture_stop(global_dpi);

            http_success_response(c);
        }
        else
        {
            // 参数错误
            http_error_response(c, __LINE__, "参数验证失败");
        }
    } else {
        http_405_response(c);
    }
}

/** 1.3 pcap 管理 **/


void pcaps_handle(struct mg_connection *c, struct mg_http_message *hm)
{
    static uint16_t file_offset = sizeof("/api/sxt/pcaps/")-1;

    if (mg_http_match_uri(hm, "/api/sxt/pcaps")) {
        // 1.3.1 获取 pcap 文件列表

        if (mg_strcmp(hm->method, method_get)) {
            http_405_response(c);
            return;
        }

        int limit = 10;
        int entry_index = 0;
        int start_index = 0;
        int end_index   = 10;
        char var[16] = {'\0'};

        if (mg_http_get_var(&hm->query, "limit", var, sizeof(var)) > 0) {
            limit = atoi(var);
            if (limit < 1) limit = 10;
        }

        mg_http_get_var(&hm->query, "platform", var, sizeof(var));

        if (strcmp(var, "phone") == 0) {
            // 手机平台参数
            if (mg_http_get_var(&hm->query, "offset", var, sizeof(var)) > 0) {
                int offset = atoi(var);
                if (offset < 0) offset = 0;
                start_index = offset;
                end_index   = offset + limit;
            }
        } else {
            // 默认pc平台
            if (mg_http_get_var(&hm->query, "page", var, sizeof(var)) > 0) {
                int page = atoi(var);
                if (page < 1) page = 1;
                start_index = (page - 1) * limit;
                end_index   = page * limit;
            }
        }

        DIR *pdir = opendir(global_dpi->pcaps_dump_dir);

        if (pdir == NULL) {
            http_error_response(c, __LINE__, "目录读取失败");
            return;
        }

        struct dirent *entry;
        struct stat file_stat;
        char full_path[PATH_MAX];
        char strtm[64];

        cJSON *res, *data, *pcaps = cJSON_CreateArray();

        while ((entry = readdir(pdir)) != NULL) {
            if (entry->d_name[0] == '.') continue;

            snprintf(full_path, sizeof(full_path), "%s/%s", global_dpi->pcaps_dump_dir, entry->d_name);

            if (stat(full_path, &file_stat) == -1) continue;
            if (!S_ISREG(file_stat.st_mode)) continue;

            if (entry_index >= start_index && entry_index < end_index) {
                cJSON *pcaps_item = cJSON_CreateObject();
                cJSON_AddItemToArray(pcaps, pcaps_item);

                cJSON_AddStringToObject(pcaps_item, "name", entry->d_name);
                cJSON_AddNumberToObject(pcaps_item, "size", file_stat.st_size);
                cJSON_AddStringToObject(pcaps_item, "createTime", time_format(&file_stat.st_ctime, strtm, sizeof(strtm)));
            }

            entry_index++;
        }

        closedir(pdir);

        res = cJSON_CreateObject();
        cJSON_AddNumberToObject(res, "code", 0);
        cJSON_AddStringToObject(res, "msg", "ok");

        data = cJSON_AddObjectToObject(res, "data");
        cJSON_AddNumberToObject(data, "totalCnt", entry_index);
        cJSON_AddNumberToObject(data, "totalPage", entry_index / limit +1);
        // cJSON_AddNumberToObject(data, "currentPage", );
        cJSON_AddItemToObject  (data, "pcaps", pcaps);

        char *res_str = cJSON_PrintUnformatted(res);

        mg_http_reply(c, 200, public_res_headers_with_josn, res_str);

        cJSON_Delete(res);
        cJSON_free(res_str);

    }
    else
    if (mg_http_match_uri(hm, "/api/sxt/pcaps/*")) {
        char file_path[PATH_MAX], real_path[PATH_MAX];
        char file_name[256], var[16];

        strncpy(file_name, hm->uri.ptr+file_offset, hm->uri.len-file_offset);
        file_name[hm->uri.len-file_offset] = '\0';

        snprintf(file_path, sizeof(file_path), "%s/%s", global_dpi->pcaps_dump_dir, file_name);

        if (realpath(file_path, real_path)) {
            LOG(LL_INFO, ("real_path: %s", real_path));

            if (strncmp(real_path, global_dpi->pcaps_dump_dir, strlen(global_dpi->pcaps_dump_dir))) {
                http_403_response(c);
                return;
            }

            strcpy(file_path, real_path);
        }

        if (!mg_strcmp(hm->method, method_get)) {

            if (mg_http_get_var(&hm->query, "download", var, sizeof(var)) > 0) {
                // 1.3.2 下载 pcap
                LOG(LL_INFO, ("下载文件: %s", file_path));
                char header[1024];
                snprintf(header, sizeof(header), "Content-Disposition: attachment; filename=\"%s\"\r\n", file_name);
                mg_http_serve_file(c, hm, file_path, "application/octet-stream", header);
            } else {
                cJSON *res = cJSON_CreateObject();
                struct stat st;
                if (stat(file_path, &st) == 0) {
                    char strtm[64] = {'\0'};
                    cJSON *data = cJSON_AddObjectToObject(res, "data");
                    cJSON_AddNumberToObject(data, "size", st.st_size);
                    cJSON_AddStringToObject(data, "createTime", time_format(&st.st_ctime, strtm, sizeof(strtm)));

                    cJSON_AddNumberToObject(res, "code", 0);
                    cJSON_AddStringToObject(res, "msg", "ok");
                } else {
                    cJSON_AddNumberToObject(res, "code", 1);
                    cJSON_AddStringToObject(res, "msg", "failed");
                }

                char *res_str = cJSON_PrintUnformatted(res);
                mg_http_reply(c, 200, public_res_headers_with_josn, res_str);

                cJSON_free(res_str);
                cJSON_Delete(res);
            }
        }
        else
        if (!mg_strcmp(hm->method, method_delete))
        {
            // 1.3.3 删除 pcap
            LOG(LL_INFO, ("删除文件: %s", file_path));

            struct stat st;

            if (stat(file_path, &st) < 0) {
                http_error_response(c, __LINE__, "文件不存在");
            } else {
                remove(file_path);
                http_success_response(c);
            }

        } else {
            http_405_response(c);
        }
    }
    else {
        http_404_response(c, hm->uri);
    }
}

/** 1.4 插件管理 **/
void plugin_handle(struct mg_connection *c, struct mg_http_message *hm)
{
    LOG(LL_DEBUG, (__func__));

    static const int plugin_offset = sizeof("/api/sxt/plugins/") -1;

    if (mg_http_match_uri(hm, "/api/sxt/plugins/*")) {

        char file_path[PATH_MAX] = {0};
        char file_name[256] = {0};

        size_t filename_len = hm->uri.len - plugin_offset;
        if (filename_len >= 256) {
            http_error_response(c, __LINE__, "插件文件名称太长, 必须小于256字节");
            return;
        }

        strncpy(file_name, hm->uri.ptr+plugin_offset, filename_len);
        snprintf(file_path, sizeof(file_path), "%s/%s", global_dpi->root_dir, file_name);

        if (!mg_strcmp(hm->method, method_post)) {
            // 1.4.1 上传插件
            // 校验格式
            if (check_plugin_vaild(hm->body.ptr, hm->body.len)) {
                http_error_response(c, __LINE__, "not shared library");
                return;
            }

            // 校验 和当前使用的插件是否一致
            if (strcmp(global_dpi->last_plugin_file, file_path) == 0) {
                http_error_response(c, __LINE__, "插件已存在");
                return;
            }

            // 接收存盘
            FILE *fp = fopen(file_path, "wb");
            if (fp == NULL) {
                http_error_response(c, __LINE__, "file open failed");
                return;
            }
            fwrite(hm->body.ptr, 1, hm->body.len, fp);
            fclose(fp);

            // 停止dpi
            dpi_opt_stop(global_dpi);
            // 更新链接
            remove(global_dpi->plugin_sylink_file);
            symlink(file_path, global_dpi->plugin_sylink_file);

            // 重启 dpi
            dpi_opt_start(global_dpi);
            global_dpi->pid = dpi_is_running(global_dpi);
            // 启动失败, 回退插件, 重新启动
            if (global_dpi->pid == 0) {
                remove(global_dpi->plugin_sylink_file);
                symlink(global_dpi->last_plugin_file, global_dpi->plugin_sylink_file);
                dpi_opt_start(global_dpi);
                global_dpi->pid = dpi_is_running(global_dpi);
                http_error_response(c, __LINE__, "插件无法使用");
                return;
            }

            strcpy(global_dpi->last_plugin_file, file_path);

            http_success_response(c);
        } else {
            http_405_response(c);
        }
    } else {
        http_404_response(c, hm->uri);
    }

}


/** test **/
void test_handle(struct mg_connection *c, struct mg_http_message *hm)
{
    char uri[512];
    bzero(uri, 512);
    strncpy(uri, hm->uri.ptr, hm->uri.len);

    if (strncmp("/download/", hm->uri.ptr, sizeof("/download/")-1) == 0)
    {
        char file_path[256] = "/home/<USER>/";
        char file_name[256] = {0};

        strncat(file_path, hm->uri.ptr + sizeof("/download/")-1, hm->uri.len - sizeof("/download/")+1);

        printf("download: %s\n", file_path);

        mg_http_serve_file(c, hm, file_path, guess_content_type(file_path), "Content-Disposition: attachment; filename=\"123.txt\"\r\n");
        return;
    }

    struct mg_http_serve_opts opts = {
        .root_dir = "/home/<USER>",
        .ssi_pattern = NULL,
    };

    mg_http_serve_dir(c, hm, &opts);
}

#endif // RESTful 接口

/** 主路由分发  **/
static void http_callbk(struct mg_connection *c, int ev, void *ev_data, void *fn_data)
{
    if (ev == MG_EV_HTTP_MSG)
    {
        struct mg_http_message *hm = (struct mg_http_message *) ev_data;

        print_request_log(c, hm);

        if (!mg_strcmp(hm->method, method_options)) {
            mg_http_reply(c, 200, public_res_headers, "");
            return;
        }

        if (mg_http_match_uri(hm, "/api/sxt/dpi/*")) {
            dpi_handle(c, hm);
        }
        else
        if (mg_http_match_uri(hm, "/api/sxt/capture")) {
            capture_handle(c, hm);
        }
        else
        if (mg_http_match_uri(hm, "/api/sxt/pcaps#")) {
            pcaps_handle(c, hm);
        }
        else
        if (mg_http_match_uri(hm, "/api/sxt/plugins#")) {
            plugin_handle(c, hm);
        }
        else {
            struct mg_http_serve_opts opts = {.root_dir = "."};
            mg_http_serve_dir(c, hm, &opts);
        }
    }
}


/** 参数解析 **/
int parse_arguments(int argc, char *argv[])
{
    int i;
    for (i = 1; i < argc; i++) {
        printf("%s\n", argv[i]);

        if (!strcmp(argv[i], "--dpi-name")) {
            strcpy(global_dpi->name, argv[++i]);
            fprintf(stdout, "设置 dpi 程序名称: %s\n", global_dpi->name);
        }
        else if (!strcmp(argv[i], "--dpi-root-dir")) {
            strcpy(global_dpi->root_dir, argv[++i]);
            size_t l = strlen(global_dpi->root_dir);
            if (global_dpi->root_dir[l-1] == '/')
                global_dpi->root_dir[l-1] = '\0';

            fprintf(stdout, "设置 dpi 运行目录: %s\n", global_dpi->root_dir);
        }
        else if (!strcmp(argv[i], "--pcaps-dump-dir")) {
            strcpy(global_dpi->pcaps_dump_dir, argv[++i]);
            size_t l = strlen(global_dpi->pcaps_dump_dir);
            if (global_dpi->pcaps_dump_dir[l-1] == '/')
                global_dpi->pcaps_dump_dir[l-1] = '\0';

            fprintf(stdout, "设置 pcaps 落盘目录: %s\n", global_dpi->pcaps_dump_dir);
        }
        else if (!strcmp(argv[i], "--port")) {
            listen_port = atoi(argv[++i]);
            fprintf(stdout, "设置 监听端口: %u\n", listen_port);
        }
        else if (!strcmp(argv[i], "--log-level")) {
            global_dpi->log_level = argv[++i];
            fprintf(stdout, "设置 日志等级: %s\n", global_dpi->log_level);
        }
        else if (!strcmp(argv[i], "-h") || !strcmp(argv[i], "--help"))
        {
            fprintf(stdout, "Options: \n");
            fprintf(stdout, "  --dpi-name           :dpi 程序名称. 默认 'yaslc'\n");
            fprintf(stdout, "  --dpi-root-dir       :dpi 运行目录, 绝对路径. 默认 '/root/ipc'\n");
            fprintf(stdout, "  --pcaps-dump-dir     :pcaps 落盘目录, 默认 /home/<USER>/dump\n");
            fprintf(stdout, "  --port               :监听端口. 默认 %u\n", listen_port);
            fprintf(stdout, "  --log-level           :日志等级, 0,1,2,3 等级越高输出越详细\n");

            fprintf(stdout, "\nExample: %s --dpi-root-dir /root/ipc\n", argv[0]);

            fprintf(stdout, "\nNote: dpi程序启动脚本中需要设置为后台运行，否则主程序会阻塞\n");

            fprintf(stdout, "\nVersion %s, build at "__DATE__"\n", main_version());

            return -1;
        }
        else if (!strcmp(argv[i], "-v") || !strcmp(argv[i], "--version")) {
            fprintf(stdout, "Version: %s, build at: "__DATE__"\n", main_version());
        }
        else {
            fprintf(stderr, "Unknown argument: %s\n", argv[i]);
        }
    }

    return 0;
}


int main(int argc, char **argv)
{   
    global_dpi = malloc(sizeof(*global_dpi));
    memset(global_dpi, 0, sizeof(*global_dpi));

    global_dpi->log_level = "2";
    global_dpi->capturing = 0;
    strcpy(global_dpi->name, "yaslc");
    strcpy(global_dpi->root_dir, "/root/ipc");
    strcpy(global_dpi->pcaps_dump_dir, "/home/<USER>/dump");

    if (parse_arguments(argc, argv) != 0) {
        exit(1);
    }

    sprintf(global_dpi->capture_file, "%s/%s", global_dpi->root_dir, "yaSlc_capture");
    sprintf(global_dpi->stats_file, "%s/%s", global_dpi->root_dir, "yaSlc_status");
    sprintf(global_dpi->plugin_sylink_file, "%s/%s", global_dpi->root_dir, "libipc.so");
    readlink(global_dpi->plugin_sylink_file, global_dpi->last_plugin_file, PATH_MAX);

    mkdirs(global_dpi->pcaps_dump_dir);

    global_dpi->pid = dpi_is_running(global_dpi);
    if (global_dpi->pid == 0) {
        dpi_opt_start(global_dpi);
        global_dpi->pid = dpi_is_running(global_dpi);
    }

    if (global_dpi->pid == 0) {
        fprintf(stderr, "dpi 程序启动失败\n");
        exit(1);
    }

    struct mg_mgr   mgr;
    struct mg_connection *c;

    mg_mgr_init(&mgr);
    mg_log_set(global_dpi->log_level);

    char addr[128];
    sprintf(addr, "http://0.0.0.0:%u", listen_port);

    c = mg_http_listen(&mgr, addr, http_callbk, NULL);

    if (c == NULL) {
        LOG(LL_ERROR, ("http server 启动失败"));
        exit(1);
    }

    LOG(LL_INFO, ("http server 启动"));

    signal(SIGINT, sig_handle);
    signal(SIGTERM, sig_handle);

    while (!should_stop) {
        mg_mgr_poll(&mgr, 1000);
    }

    dpi_opt_stop(global_dpi);

    mg_mgr_free(&mgr);
    return 0;
}
