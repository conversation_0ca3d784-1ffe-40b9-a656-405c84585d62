CFLAGS += -I./cjson -I./mongoose -g -Wall -Wno-unused-variable -I/media/yview/work/wifi6e/Src_NorFlash/package/iniparser3/src/src
SRCS = mongoose/mongoose.c cjson/cJSON.c cjson/cJSON_Utils.c
LD_FLAGS += -Wl,-Bstatic -liniparser -Wl,-Bdynamic -L/media/yview/work/wifi6e/Src_NorFlash/package/iniparser3/src

http_server: main.c
	gcc -o $@ -DMG_ENABLE_DIRECTORY_LISTING=1 main.c ${SRCS} ${CFLAGS} ${LD_FLAGS}

.PHONY: clean

clean:
	rm -f http_server
