cmake_minimum_required(VERSION 3.14)

# project
project(http_server LANGUAGES C CXX VERSION 0.0.1)
option(USE_PKG_CONFIG_TO_LINK off)

# set(CMAKE_C_STANDARD 17)

#
# ftyepsTest
#
include_directories(
  ${CMAKE_SOURCE_DIR}/include)

add_executable(http_server
  main.c
  mongoose/mongoose.c
  cjson/cJSON.c
  cjson/cJSON_Utils.c
)
set(CMAKE_C_FLAGS "-I./cjson -I./mongoose -g -Wall -Wno-unused-variable")
link_directories(CMAKE_SOURCE_DIR/lib/amd64) 

target_link_directories(http_server PRIVATE
  ${CMAKE_SOURCE_DIR}/lib/amd64
)
target_link_libraries(http_server
  iniparser
)
