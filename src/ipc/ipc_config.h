
#include "ipc/ipc.h"

#ifndef __CODECS_IPC_CONFIG_H__
#define __CODECS_IPC_CONFIG_H__
class ipc_cfg {
public:
  ipc_cfg();
  int GetLogLevel() { return log_level; }

  ipc_on_new_stream     new_stream_fn = nullptr;
  ipc_on_new_nalu       new_nalu_fn = nullptr;
  uint64_t              flag = 0;
  void                 *userdata = nullptr;
  enum IPC_LOG_LEVEL_E  log_level = IPC_LOG_Error;
  enum IPC_FRAME_TYPE_E type;
  int                   tcp_rsm_out_of_order = 0;
  uint32_t              exclude_packet_ip = 0;
};

#endif