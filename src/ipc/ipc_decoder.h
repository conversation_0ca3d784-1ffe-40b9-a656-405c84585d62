/**
 * @file ipc_decoder.h
 * <AUTHOR> (<EMAIL>)
 * @brief [接口层]类
 * @version 0.1
 * @date 2024-07-10
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#ifndef __CODECS_IPC_DECODER_H__
#define __CODECS_IPC_DECODER_H__

#include "slc_config.h"
#include "common/tools.h"
#include "slc_capture.h"
#include "slc_nalu.h"
#include "slc_logger.h"
#include "ipc/ipc_stream.h"
#include "ipc_config.h"
#include "slc_flow.h"
#include "rtp_h264_packer.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef __cplusplus
}
#endif /* __cplusplus */

static uint64_t getStreamidFromTuple(five_tuple* key, uint64_t flow_ssrc) {
  std::string ip_dst((char*)&key->ip_dst, 16);
  std::string ip_src((char*)&key->ip_src, 16);

  Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t> tuple_key(
      ip_dst, ip_src, key->port_src, key->port_dst, key->ip_version, key->proto);

  hash_func func;
  size_t    h = std::hash<uint64_t>()(flow_ssrc);
  size_t    h1 = func(tuple_key);
  return h ^ h1;
}

/**

 * @brief 通过接口创建decoder 可以同时存在多个decoder实例，
 * 每个decoder都有自己的flow管理器与stream管理器
 * 
 */
class ipc_decoder {
public:
  ipc_decoder(ipc_cfg* ipc_conf);
  ~ipc_decoder();
  friend class SlcProtoRegBase;

  ipc_stream_info* getStreamInfo(uint64_t stream_id);

  void onProcessPkt(const uint8_t* buff, int len);

  int        onNaluCallback(NaluType_e naluType, uint8_t* nalu, int naluLen, void* userdata);
  int        onNaluCallback(NaluType_e naluType, uint8_t* nalu, int naluLen, flow_info* flow_info);
  flow_info* findCreateFlow(five_tuple* key);
  int        getTcpRsmOutOfOrder() { return cfg_->tcp_rsm_out_of_order; }

  void setIpcStreamCapability(uint64_t stream_id, struct ipc_capability_t& capability);
  void creatIpcStreamFromSip(void* user);
  void creatIpcStreamFromRTSP(void* rtsp_stream, void* rtp_stream,  uint32_t ssrc,  RtpMediaType MediaType ,uint8_t* sps, int sps_len, uint8_t* pps, int pps_len);
  void updateCapabilitynum(std::shared_ptr<ipc_stream_info> p);
  std::shared_ptr<ipc_stream_info> creatIpcStrem(uint64_t stream_id, flow_info* stream);
  std::shared_ptr<ipc_stream_info> findCreatIpcStrem(void* protoStream, int64_t flow_ssrc);

  void onNewIpcStreamFunc(std::shared_ptr<ipc_stream_info> p);
  ipc_cfg* getDecoderConfig(){
    return cfg_;
  }
private:
  slc::Logger ipc_logger;
  ipc_cfg*    cfg_;

  //flow管理器
  std::unordered_map<Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t>, std::shared_ptr<flow_info>, hash_func>
      flow_map;

  //nalu流管理器
  // nalu的stream_id 还是调用 hash_func(Node) 生成的
  std::map<uint64_t, std::shared_ptr<ipc_stream_info>> nalu_stream_map;

  //构造时要将config中的回调函数登记
  ipc_on_new_stream new_stream_fn;
  ipc_on_new_nalu   new_nalu_fn;
  void*             user_;  //回调函数的上下文传递器
};

#endif