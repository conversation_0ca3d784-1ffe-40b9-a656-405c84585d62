#include "ipc/ipc_msg.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

#define IPC_PROTO_VERSION 10  // v1.0

struct ipc_message* ipc_msg_create(uint8_t msg_type, uint16_t transaction_id, 
                                   const void* body_data, uint32_t body_len)
{
    if (body_len > 0 && body_data == NULL) {
        return NULL;
    }

    // 分配内存：消息头 + 消息体
    size_t total_size = sizeof(struct ipc_msg_hdr) + body_len;
    struct ipc_message* msg = (struct ipc_message*)malloc(total_size);
    if (msg == NULL) {
        return NULL;
    }

    // 填充消息头
    msg->hdr.proto_version = IPC_PROTO_VERSION;
    msg->hdr.msg_type = msg_type;
    msg->hdr.transaction_id = transaction_id;
    msg->hdr.msg_len = body_len;

    // 复制消息体
    if (body_len > 0 && body_data != NULL) {
        memcpy(&msg->body, body_data, body_len);
    }

    return msg;
}

void ipc_msg_free(struct ipc_message* msg)
{
    if (msg != NULL) {
        free(msg);
    }
}

uint32_t ipc_msg_total_len(const struct ipc_message* msg)
{
    if (msg == NULL) {
        return 0;
    }
    return sizeof(struct ipc_msg_hdr) + msg->hdr.msg_len;
}

int ipc_msg_validate(const uint8_t* data, uint32_t len)
{
    if (data == NULL || len < sizeof(struct ipc_msg_hdr)) {
        return -1;
    }

    const struct ipc_msg_hdr* hdr = (const struct ipc_msg_hdr*)data;
    
    // 检查协议版本
    if (hdr->proto_version != IPC_PROTO_VERSION) {
        return -1;
    }

    // 检查消息类型
    if (hdr->msg_type < IPC_MSG_HELLO || hdr->msg_type > IPC_MSG_DUMP_DONE) {
        return -1;
    }

    // 检查消息长度
    uint32_t expected_total_len = sizeof(struct ipc_msg_hdr) + hdr->msg_len;
    if (len != expected_total_len) {
        return -1;
    }

    // 根据消息类型验证消息体长度
    switch (hdr->msg_type) {
        case IPC_MSG_HELLO:
            if (hdr->msg_len != sizeof(struct ipc_msg_hello)) {
                return -1;
            }
            break;
        case IPC_MSG_HEART_BEAT:
            if (hdr->msg_len != sizeof(struct ipc_msg_heart_beat)) {
                return -1;
            }
            break;
        case IPC_MSG_STREAM_DETECTED:
            if (hdr->msg_len != sizeof(struct ipc_msg_stream_detected)) {
                return -1;
            }
            break;
        case IPC_MSG_DUMP_START:
            if (hdr->msg_len != sizeof(struct ipc_msg_dump_start)) {
                return -1;
            }
            break;
        case IPC_MSG_DUMP_STOP:
        case IPC_MSG_DUMP_DONE:
            if (hdr->msg_len != 0) {
                return -1;
            }
            break;
        case IPC_MSG_STREAM_NALU:
            // NALU消息的消息体长度至少包含streamID和nalu_seq
            if (hdr->msg_len < sizeof(uint32_t) * 2) {
                return -1;
            }
            break;
        default:
            return -1;
    }

    return 0;
}

// 辅助函数：创建特定类型的消息

struct ipc_message* ipc_msg_create_hello(uint16_t transaction_id, 
                                          uint8_t dpi_version, 
                                          uint8_t plugin_version,
                                          const char* plugin_path)
{
    struct ipc_msg_hello hello_body;
    memset(&hello_body, 0, sizeof(hello_body));
    
    hello_body.dpi_version = dpi_version;
    hello_body.plugin_version = plugin_version;
    
    if (plugin_path != NULL) {
        strncpy(hello_body.plugin_path, plugin_path, sizeof(hello_body.plugin_path) - 1);
        hello_body.plugin_path[sizeof(hello_body.plugin_path) - 1] = '\0';
    }
    
    return ipc_msg_create(IPC_MSG_HELLO, transaction_id, &hello_body, sizeof(hello_body));
}

struct ipc_message* ipc_msg_create_heartbeat(uint16_t transaction_id,
                                             uint8_t dpi_status,
                                             uint8_t stream_cnt,
                                             const char* msg)
{
    struct ipc_msg_heart_beat hb_body;
    memset(&hb_body, 0, sizeof(hb_body));
    
    hb_body.dpi_status = dpi_status;
    hb_body.stream_cnt = stream_cnt;
    
    if (msg != NULL) {
        strncpy(hb_body.msg, msg, sizeof(hb_body.msg) - 1);
        hb_body.msg[sizeof(hb_body.msg) - 1] = '\0';
    }
    
    return ipc_msg_create(IPC_MSG_HEART_BEAT, transaction_id, &hb_body, sizeof(hb_body));
}

struct ipc_message* ipc_msg_create_stream_detected(uint16_t transaction_id,
                                                   uint32_t streamID,
                                                   uint32_t flag,
                                                   const char* proto)
{
    struct ipc_msg_stream_detected sd_body;
    memset(&sd_body, 0, sizeof(sd_body));
    
    sd_body.streamID = streamID;
    sd_body.flag = flag;
    
    if (proto != NULL) {
        strncpy(sd_body.proto, proto, sizeof(sd_body.proto) - 1);
        sd_body.proto[sizeof(sd_body.proto) - 1] = '\0';
    }
    
    return ipc_msg_create(IPC_MSG_STREAM_DETECTED, transaction_id, &sd_body, sizeof(sd_body));
}

struct ipc_message* ipc_msg_create_nalu(uint16_t transaction_id,
                                        uint32_t streamID,
                                        uint32_t nalu_seq,
                                        const uint8_t* nalu_data,
                                        uint32_t nalu_len)
{
    if (nalu_data == NULL || nalu_len == 0) {
        return NULL;
    }
    
    // 计算消息体总长度
    uint32_t body_len = sizeof(uint32_t) * 2 + nalu_len; // streamID + nalu_seq + nalu_data
    
    // 分配内存
    size_t total_size = sizeof(struct ipc_msg_hdr) + body_len;
    struct ipc_message* msg = (struct ipc_message*)malloc(total_size);
    if (msg == NULL) {
        return NULL;
    }
    
    // 填充消息头
    msg->hdr.proto_version = IPC_PROTO_VERSION;
    msg->hdr.msg_type = IPC_MSG_STREAM_NALU;
    msg->hdr.transaction_id = transaction_id;
    msg->hdr.msg_len = body_len;
    
    // 填充消息体
    uint8_t* body_ptr = (uint8_t*)&msg->body;
    *((uint32_t*)body_ptr) = streamID;
    body_ptr += sizeof(uint32_t);
    *((uint32_t*)body_ptr) = nalu_seq;
    body_ptr += sizeof(uint32_t);
    memcpy(body_ptr, nalu_data, nalu_len);
    
    return msg;
}

struct ipc_message* ipc_msg_create_dump_start(uint16_t transaction_id,
                                              uint32_t dump_size,
                                              const char* brand,
                                              const char* serial)
{
    struct ipc_msg_dump_start ds_body;
    memset(&ds_body, 0, sizeof(ds_body));
    
    ds_body.dump_size = dump_size;
    
    if (brand != NULL) {
        strncpy(ds_body.brand, brand, sizeof(ds_body.brand) - 1);
        ds_body.brand[sizeof(ds_body.brand) - 1] = '\0';
    }
    
    if (serial != NULL) {
        strncpy(ds_body.serial, serial, sizeof(ds_body.serial) - 1);
        ds_body.serial[sizeof(ds_body.serial) - 1] = '\0';
    }
    
    return ipc_msg_create(IPC_MSG_DUMP_START, transaction_id, &ds_body, sizeof(ds_body));
}

struct ipc_message* ipc_msg_create_simple(uint8_t msg_type, uint16_t transaction_id)
{
    // 用于创建不需要消息体的消息，如DUMP_STOP和DUMP_DONE
    if (msg_type != IPC_MSG_DUMP_STOP && msg_type != IPC_MSG_DUMP_DONE) {
        return NULL;
    }
    
    return ipc_msg_create(msg_type, transaction_id, NULL, 0);
}
