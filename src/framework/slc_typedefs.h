/*
 * nslc_typedefs.h
 *
 * Copyright (C) 2011-16 - ntop.org
 *
 * This file is part of nSLC, an open source deep packet inspection
 * library based on the OpenSLC and PACE technology by ipoque GmbH
 *
 * nSLC is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * nSLC is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with nSLC.  If not, see <http://www.gnu.org/licenses/>.
 *
 */

#ifndef __SLC_TYPEDEFS_H__
#define __SLC_TYPEDEFS_H__
#include <sys/types.h>
#include <stdint.h>
#include <slc_etypes.h>

#define __OpenBSD__

#ifdef __OpenBSD__
#include <endian.h>
//#define __BYTE_ORDER BYTE_ORDER
#if BYTE_ORDER == LITTLE_ENDIAN
#define __LITTLE_ENDIAN__
#else
#define __BIG_ENDIAN__
#endif /* BYTE_ORDER */
#endif /* __OPENBSD__ */

/* Definitions of OSI NLPIDs <Network Layer Protocol IDs> X.263*/
#define NLPID_NULL 0x00
#define NLPID_IPI_T_70 0x01 /* T.70, when an IPI */
#define NLPID_SPI_X_29 0x01 /* X.29, when an SPI */
#define NLPID_X_633 0x03    /* X.633 */
#define NLPID_DMS 0x03      /* Maintenace messages: AT&T TR41459, Nortel NIS A211-1, Telcordia SR-4994, ... */
#define NLPID_Q_931 0x08    /* Q.931, Q.932, X.36, ISO 11572, ISO 11582 */
#define NLPID_Q_933 0x08    /* Q.933, on Frame Relay */
#define NLPID_Q_2931 0x09   /* Q.2931 */
#define NLPID_Q_2119 0x0c   /* Q.2119 */
#define NLPID_SNAP 0x80
#define NLPID_ISO8473_CLNP 0x81 /* X.233 */
#define NLPID_ISO9542_ESIS 0x82
#define NLPID_ISO10589_ISIS 0x83
#define NLPID_ISO10747_IDRP 0x85
#define NLPID_ISO9542X25_ESIS 0x8a
#define NLPID_ISO10030 0x8c
#define NLPID_ISO11577 0x8d /* X.273 */
#define NLPID_IP6 0x8e
#define NLPID_COMPRESSED 0xb0 /* "Data compression protocol" */
#define NLPID_TRILL 0xc0
#define NLPID_SNDCF 0xc1 /* "SubNetwork Dependent Convergence Function */
#define NLPID_IEEE_8021AQ \
  0xc1 /* IEEE 802.1aq (draft-ietf-isis-ieee-aq-05.txt); defined in context of ISIS "supported protocols" TLV */
#define NLPID_IP 0xcc
#define NLPID_PPP 0xcf

/* # $Id: protocols,v 1.11 2011/05/03 14:45:40 ovasik Exp $ */
/* # */
/* # Internet (IP) protocols */
/* # */
/* #    from: @(#)protocols 5.1 (Berkeley) 4/17/89 */
/* # */
/* # Updated for NetBSD based on RFC 1340, Assigned Numbers (July 1992). */
/* # Last IANA update included dated 2011-05-03 */
/* # */
/* # See also http://www.iana.org/assignments/protocol-numbers */

#define IP_PROTO_IP 0                /*  IP               # internet protocol, pseudo protocol number */
#define IP_PROTO_HOPOPT 0            /*  HOPOPT           # hop_by_hop options for ipv6 */
#define IP_PROTO_ICMP 1              /*  ICMP             # internet control message protocol */
#define IP_PROTO_IGMP 2              /*  IGMP             # internet group management protocol */
#define IP_PROTO_GGP 3               /*  GGP              # gateway_gateway protocol */
#define IP_PROTO_IPV4 4              /*  IPv4             # IPv4 encapsulation */
#define IP_PROTO_ST 5                /*  ST               # ST datagram mode */
#define IP_PROTO_TCP 6               /*  TCP              # transmission control protocol */
#define IP_PROTO_CBT 7               /*  CBT              # <AUTHOR> <EMAIL> */
#define IP_PROTO_EGP 8               /*  EGP              # exterior gateway protocol */
#define IP_PROTO_IGP 9               /*  IGP              # any private interior gateway (Cisco: for IGRP) */
#define IP_PROTO_BBN_RCC 10          /*  BBN_RCC_MON      # BBN RCC Monitoring */
#define IP_PROTO_NVP 11              /*  NVP_II           # Network Voice Protocol */
#define IP_PROTO_PUP 12              /*  PUP              # PARC universal packet protocol */
#define IP_PROTO_ARGUS 13            /*  ARGUS            # ARGUS */
#define IP_PROTO_EMCON 14            /*  EMCON            # EMCON */
#define IP_PROTO_XNET 15             /*  XNET             # Cross Net Debugger */
#define IP_PROTO_CHAOS 16            /*  CHAOS            # Chaos */
#define IP_PROTO_UDP 17              /*  UDP              # user datagram protocol */
#define IP_PROTO_MUX 18              /*  MUX              # Multiplexing protocol */
#define IP_PROTO_DCN 19              /*  DCN_MEAS         # DCN Measurement Subsystems */
#define IP_PROTO_HMP 20              /*  HMP              # host monitoring protocol */
#define IP_PROTO_PRM 21              /*  PRM              # packet radio measurement protocol */
#define IP_PROTO_XNS_IDP 22          /*  XNS_IDP          # Xerox NS IDP */
#define IP_PROTO_TRUNK_1 23          /*  TRUNK_1          # Trunk_1 */
#define IP_PROTO_TRUNK_2 24          /*  TRUNK_2          # Trunk_2 */
#define IP_PROTO_LEAF_1 25           /*  LEAF_1           # Leaf_1 */
#define IP_PROTO_LEAF_2 26           /*  LEAF_2           # Leaf_2 */
#define IP_PROTO_RDP 27              /*  RDP              # "reliable datagram" protocol */
#define IP_PROTO_IRTP 28             /*  IRTP             # Internet Reliable Transaction Protocol */
#define IP_PROTO_ISO_TP4 29          /*  ISO_TP4          # ISO Transport Protocol Class 4 */
#define IP_PROTO_NETBLT 30           /*  NETBLT           # Bulk Data Transfer Protocol */
#define IP_PROTO_MFE_NSP 31          /*  MFE_NSP          # MFE Network Services Protocol */
#define IP_PROTO_MERIT_INP 32        /*  MERIT_INP        # MERIT Internodal Protocol */
#define IP_PROTO_DCCP 33             /*  DCCP             # Datagram Congestion Control Protocol */
#define IP_PROTO_3PC 34              /*  3PC              # Third Party Connect Protocol */
#define IP_PROTO_IDPR 35             /*  IDPR             # Inter_Domain Policy Routing Protocol */
#define IP_PROTO_XTP 36              /*  XTP              # Xpress Tranfer Protocol */
#define IP_PROTO_DDP 37              /*  DDP              # Datagram Delivery Protocol */
#define IP_PROTO_IDPR_CMTP 38        /*  IDPR_CMTP        # IDPR Control Message Transport Proto */
#define IP_PROTO_TP_PLUS_PLUS 39     /*  TP++             # TP++ Transport Protocol */
#define IP_PROTO_IL 40               /*  IL               # IL Transport Protocol */
#define IP_PROTO_IPV6 41             /*  IPv6             # IPv6 encapsulation */
#define IP_PROTO_SDRP 42             /*  SDRP             # Source Demand Routing Protocol */
#define IP_PROTO_IPV6_ROUTE 43       /*  IPv6_Route       # Routing Header for IPv6 */
#define IP_PROTO_IPV6_FRAG 44        /*  IPv6_Frag        # Fragment Header for IPv6 */
#define IP_PROTO_IDRP 45             /*  IDRP             # Inter_Domain Routing Protocol */
#define IP_PROTO_RSVP 46             /*  RSVP             # Resource ReSerVation Protocol */
#define IP_PROTO_GRE 47              /*  GRE              # Generic Routing Encapsulation */
#define IP_PROTO_DSR 48              /*  DSR              # Dynamic Source Routing Protocol */
#define IP_PROTO_BNA 49              /*  BNA              # BNA */
#define IP_PROTO_ESP 50              /*  ESP              # Encap Security Payload */
#define IP_PROTO_IPV6_CRYPT 50       /*  IPv6_Crypt       # Encryption Header for IPv6 (not in official list) */
#define IP_PROTO_AH 51               /*  AH               # Authentication Header */
#define IP_PROTO_IPV6_AUTH 51        /*  IPv6_Auth        # Authentication Header for IPv6 (not in official list) */
#define IP_PROTO_I_NLSP 52           /*  I_NLSP           # Integrated Net Layer Security TUBA */
#define IP_PROTO_SWIPE 53            /*  SWIPE            # IP with Encryption */
#define IP_PROTO_NARP 54             /*  NARP             # NBMA Address Resolution Protocol */
#define IP_PROTO_MOBILE 55           /*  MOBILE           # IP Mobility */
#define IP_PROTO_TLSP 56             /*  TLSP             # Transport Layer Security Protocol */
#define IP_PROTO_SKIP 57             /*  SKIP             # SKIP */
#define IP_PROTO_IPV6_ICMP 58        /*  IPv6_ICMP        # ICMP for IPv6 */
#define IP_PROTO_IPV6_NONXT 59       /*  IPv6_NoNxt       # No Next Header for IPv6 */
#define IP_PROTO_IPV6_OPTS 60        /*  IPv6_Opts        # Destination Options for IPv6 */
#define IP_PROTO_NONE_61 61          /*                   # any host internal protocol */
#define IP_PROTO_CFTP 62             /*  CFTP             # CFTP */
#define IP_PROTO_NONE_63 63          /*                   # any local network */
#define IP_PROTO_SAT_EXPAK 64        /*  SAT_EXPAK        # SATNET and Backroom EXPAK */
#define IP_PROTO_KRYPTOLAN 65        /*  KRYPTOLAN        # Kryptolan */
#define IP_PROTO_RVD 66              /*  RVD              # MIT Remote Virtual Disk Protocol */
#define IP_PROTO_IPPC 67             /*  IPPC             # Internet Pluribus Packet Core */
#define IP_PROTO_NONE_68 68          /*                   # any distributed file system */
#define IP_PROTO_SAT_MON 69          /*  SAT_MON          # SATNET Monitoring */
#define IP_PROTO_VISA 70             /*  VISA             # VISA Protocol */
#define IP_PROTO_IPCV 71             /*  IPCV             # Internet Packet Core Utility */
#define IP_PROTO_CPNX 72             /*  CPNX             # Computer Protocol Network Executive */
#define IP_PROTO_CPHB 73             /*  CPHB             # Computer Protocol Heart Beat */
#define IP_PROTO_WSN 74              /*  WSN              # Wang Span Network */
#define IP_PROTO_PVP 75              /*  PVP              # Packet Video Protocol */
#define IP_PROTO_BR_SAT_MON 76       /*  BR_SAT_MON       # Backroom SATNET Monitoring */
#define IP_PROTO_SUN_ND 77           /*  SUN_ND           # SUN ND PROTOCOL_Temporary */
#define IP_PROTO_WB_MON 78           /*  WB_MON           # WIDEBAND Monitoring */
#define IP_PROTO_WB_EXPAK 79         /*  WB_EXPAK         # WIDEBAND EXPAK */
#define IP_PROTO_ISO_IP 80           /*  ISO_IP           # ISO Internet Protocol */
#define IP_PROTO_VMTP 81             /*  VMTP             # Versatile Message Transport */
#define IP_PROTO_SECURE_VMTP 82      /*  SECURE_VMTP      # SECURE_VMTP */
#define IP_PROTO_VINES 83            /*  VINES            # VINES */
#define IP_PROTO_TTP 84              /*  TTP              # TTP */
#define IP_PROTO_NSFNET_IGP 85       /*  NSFNET_IGP       # NSFNET_IGP */
#define IP_PROTO_DGP 86              /*  DGP              # Dissimilar Gateway Protocol */
#define IP_PROTO_TCF 87              /*  TCF              # TCF */
#define IP_PROTO_EIGRP 88            /*  EIGRP            # Enhanced Interior Routing Protocol (Cisco) */
#define IP_PROTO_OSPF 89             /*  OSPFIGP          # Open Shortest Path First IGP */
#define IP_PROTO_SPRITE_RPC 90       /*  Sprite_RPC       # Sprite RPC Protocol */
#define IP_PROTO_LARP 91             /*  LARP             # Locus Address Resolution Protocol */
#define IP_PROTO_MTP 92              /*  MTP              # Multicast Transport Protocol */
#define IP_PROTO_AX_25 93            /*  AX.25            # AX.25 Frames */
#define IP_PROTO_IPIP 94             /*  IPIP             # Yet Another IP encapsulation */
#define IP_PROTO_MICP 95             /*  MICP             # Mobile Internetworking Control Pro. */
#define IP_PROTO_SCC_SP 96           /*  SCC_SP           # Semaphore Communications Sec. Pro. */
#define IP_PROTO_ETHERIP 97          /*  ETHERIP          # Ethernet_within_IP Encapsulation */
#define IP_PROTO_ENCAP 98            /*  ENCAP            # Yet Another IP encapsulation */
#define IP_PROTO_NONE_99 99          /*                   # any private encryption scheme */
#define IP_PROTO_GMTP 100            /*  GMTP             # GMTP */
#define IP_PROTO_IFMP 101            /*  IFMP             # Ipsilon Flow Management Protocol */
#define IP_PROTO_PNNI 102            /*  PNNI             # PNNI over IP */
#define IP_PROTO_PIM 103             /*  PIM              # Protocol Independent Multicast */
#define IP_PROTO_ARIS 104            /*  ARIS             # ARIS */
#define IP_PROTO_SCPS 105            /*  SCPS             # SCPS */
#define IP_PROTO_QNX 106             /*  QNX              # QNX */
#define IP_PROTO_A_N 107             /*  A/N              # Active Networks */
#define IP_PROTO_IPCOMP 108          /*  IPComp           # IP Payload Compression Protocol */
#define IP_PROTO_SNP 109             /*  SNP              # Sitara Networks Protocol */
#define IP_PROTO_COMPAQ_PEER 110     /*  Compaq_Peer      # Compaq Peer Protocol */
#define IP_PROTO_IPX_IN_IP 111       /*  IPX_in_IP        # IPX in IP */
#define IP_PROTO_VRRP 112            /*  VRRP             # Virtual Router Redundancy Protocol */
#define IP_PROTO_PGM 113             /*  PGM              # PGM Reliable Transport Protocol */
#define IP_PROTO_NONE_114 114        /*                   # any 0_hop protocol */
#define IP_PROTO_L2TP 115            /*  L2TP             # Layer Two Tunneling Protocol */
#define IP_PROTO_DDX 116             /*  DDX              # D_II Data Exchange */
#define IP_PROTO_IATP 117            /*  IATP             # Interactive Agent Transfer Protocol */
#define IP_PROTO_STP 118             /*  STP              # Schedule Transfer */
#define IP_PROTO_SRP 119             /*  SRP              # SpectraLink Radio Protocol */
#define IP_PROTO_UTI 120             /*  UTI              # UTI */
#define IP_PROTO_SMP 121             /*  SMP              # Simple Message Protocol */
#define IP_PROTO_SM 122              /*  SM               # SM */
#define IP_PROTO_PTP 123             /*  PTP              # Performance Transparency Protocol */
#define IP_PROTO_ISIS 124            /*  ISIS             # ISIS over IPv4 */
#define IP_PROTO_FIRE 125            /*  FIRE */
#define IP_PROTO_CRTP 126            /*  CRTP             # Combat Radio Transport Protocol */
#define IP_PROTO_CRDUP 127           /*  CRUDP            # Combat Radio User Datagram */
#define IP_PROTO_SSCOPMCE 128        /*  SSCOPMCE */
#define IP_PROTO_IPLT 129            /*  IPLT */
#define IP_PROTO_SPS 130             /*  SPS              # Secure Packet Shield */
#define IP_PROTO_PIPE 131            /*  PIPE             # Private IP Encapsulation within IP */
#define IP_PROTO_SCTP 132            /*  SCTP             # Stream Control Transmission Protocol */
#define IP_PROTO_FC 133              /*  FC               # Fibre Channel */
#define IP_PROTO_RSVP_E2E_IGNORE 134 /*  RSVP_E2E_IGNORE */
#define IP_PROTO_MOBILITY_HEADER 135 /*  Mobility_Header  # Mobility Header */
#define IP_PROTO_UDPLITE 136         /*  UDPLite */
#define IP_PROTO_MPLS_IN_IP 137      /*  MPLS_in_IP */
#define IP_PROTO_MANET 138           /*  manet            # MANET Protocols */
#define IP_PROTO_HIP 139             /*  HIP              # Host Identity Protocol */
#define IP_PROTO_SHIM6 140           /*  Shim6            # Shim6 Protocol */
#define IP_PROTO_WESP 141            /*  WESP             # Wrapped Encapsulating Security Payload */
#define IP_PROTO_ROHC 142            /*  ROHC             # Robust Header Compression */
/* #                              143-252 Unassigned                                    [IANA] */
/* #                              253     Use for experimentation and testing           [RFC3692] */
/* #                              254     Use for experimentation and testing           [RFC3692] */
/* #                              255     Reserved                                      [IANA] */

/* NSLC_VISIT */
typedef enum { nslc_preorder, nslc_postorder, nslc_endorder, nslc_leaf } nslc_VISIT;

/* NSLC_NODE */
typedef struct node_t {
  char *key;
  struct node_t *left, *right;
} nslc_node;

/* ++++++++++++++++++++++++ Cisco headers +++++++++++++++++++++ */

#define PACK_ON
#define PACK_OFF __attribute__((packed))

/* SLARP - Serial Line ARP http://tinyurl.com/qa54e95 */
PACK_ON
struct slc_slarp {
  /* address requests (0x00)
    address replies  (0x01)
    keep-alive       (0x02)
    */
  uint32_t slarp_type;
  uint32_t addr_1;
  uint32_t addr_2;
} PACK_OFF;

/* Cisco Discovery Protocol http://tinyurl.com/qa6yw9l */
PACK_ON
struct slc_cdp {
  uint8_t version;
  uint8_t ttl;
  uint16_t checksum;
  uint16_t type;
  uint16_t length;
} PACK_OFF;

/* +++++++++++++++ Ethernet header (IEEE 802.3) +++++++++++++++ */

PACK_ON
struct slc_arm_ethhdr {
  u_char h_dest[6];   /* destination eth addr */
  u_char h_source[6]; /* source ether addr    */
  uint32_t LLC;
  uint16_t h_proto; /* data length (<= 1500) or type ID proto (>=1536) */
} PACK_OFF;

PACK_ON
struct slc_ethhdr {
  u_char h_dest[6];   /* destination eth addr */
  u_char h_source[6]; /* source ether addr    */
  uint16_t h_proto;   /* data length (<= 1500) or type ID proto (>=1536) */
} PACK_OFF;

/* +++++++++++++++++++ LLC header (IEEE 802.2) ++++++++++++++++ */

PACK_ON
struct slc_snap_extension {
  uint16_t oui;
  uint8_t oui2;
  uint16_t proto_ID;
} PACK_OFF;

PACK_ON
struct slc_llc_header_snap {
  uint8_t dsap;
  uint8_t ssap;
  uint8_t ctrl;
  struct slc_snap_extension snap;
} PACK_OFF;

/* ++++++++++ RADIO TAP header (for IEEE 802.11) +++++++++++++ */
PACK_ON
struct slc_radiotap_header {
  uint8_t version; /* set to 0 */
  uint8_t pad;
  uint16_t len;
  uint32_t present;
  uint64_t MAC_timestamp;
  uint8_t flags;
} PACK_OFF;

/* ++++++++++++ Wireless header (IEEE 802.11) ++++++++++++++++ */
PACK_ON
struct slc_wifi_header {
  uint16_t fc;
  uint16_t duration;
  u_char rcvr[6];
  u_char trsm[6];
  u_char dest[6];
  uint16_t seq_ctrl;
  /* uint64_t ccmp - for data encryption only - check fc.flag */
} PACK_OFF;

/* +++++++++++++++++++++++ MPLS header +++++++++++++++++++++++ */

PACK_ON
struct slc_mpls_header {
  uint32_t label : 20, exp : 3, s : 1, ttl : 8;
} PACK_OFF;

/* ++++++++++++++++++++++++ IP header ++++++++++++++++++++++++ */

PACK_ON
struct slc_iphdr {
#if defined(__LITTLE_ENDIAN__)
  uint8_t ihl : 4, version : 4;
#elif defined(__BIG_ENDIAN__)
  uint8_t version : 4, ihl : 4;
#else
#error "Byte order must be defined"
#endif
  uint8_t tos;
  uint16_t tot_len;
  uint16_t id;
  uint16_t frag_off;
  uint8_t ttl;
  uint8_t protocol;
  uint16_t check;
  uint8_t saddr[4];
  uint8_t daddr[4];
  uint8_t option_type;
} PACK_OFF;

/* +++++++++++++++++++++++ IPv6 header +++++++++++++++++++++++ */
/* rfc3542 */

struct slc_in6_addr {
  union {
    uint8_t u6_addr8[16];
    uint16_t u6_addr16[8];
    uint32_t u6_addr32[4];
  } u6_addr; /* 128-bit IP6 address */
};

PACK_ON
struct slc_ipv6hdr {
  union {
    struct nslc_ip6_hdrctl {
      uint32_t ip6_un1_flow;
      uint16_t ip6_un1_plen;
      uint8_t ip6_un1_nxt;
      uint8_t ip6_un1_hlim;
    } ip6_un1;
    uint8_t ip6_un2_vfc;
  } ip6_ctlun;
  uint8_t ip6_src[16];
  uint8_t ip6_dst[16];

  //    struct slc_in6_addr ip6_src;
  //    struct slc_in6_addr ip6_dst;
} PACK_OFF;

/* +++++++++++++++++++++++ TCP header +++++++++++++++++++++++ */

PACK_ON
struct slc_tcphdr {
  uint16_t source;
  uint16_t dest;
  uint32_t seq;
  uint32_t ack_seq;
#if defined(__LITTLE_ENDIAN__)
  uint16_t res1 : 4, doff : 4, fin : 1, syn : 1, rst : 1, psh : 1, ack : 1, urg : 1, ece : 1, cwr : 1;
#elif defined(__BIG_ENDIAN__)
  uint16_t doff : 4, res1 : 4, cwr : 1, ece : 1, urg : 1, ack : 1, psh : 1, rst : 1, syn : 1, fin : 1;
#else
#error "Byte order must be defined"
#endif
  uint16_t window;
  uint16_t check;
  uint16_t urg_ptr;
} PACK_OFF;

/* +++++++++++++++++++++++ UDP header +++++++++++++++++++++++ */

PACK_ON
struct slc_udphdr {
  uint16_t source;
  uint16_t dest;
  uint16_t len;
  uint16_t check;
} PACK_OFF;

PACK_ON
struct slc_sctphdr {
  uint16_t source;
  uint16_t dest;
  uint32_t v_tag;
  uint32_t checksum;
} PACK_OFF;

PACK_ON
struct slc_dns_packet_header {
  uint16_t tr_id;
  uint16_t flags;
  uint16_t num_queries;
  uint16_t num_answers;
  uint16_t authority_rrs;
  uint16_t additional_rrs;
} PACK_OFF;

typedef union {
  uint32_t ipv4;
  uint8_t ipv4_uint8_t[4];
#ifdef NSLC_DETECTION_SUPPORT_IPV6
  struct slc_in6_addr ipv6;
#endif
} slc_ip_addr_t;

/* +++++++++++++++++++++++ ICMP header +++++++++++++++++++++++ */

PACK_ON
struct slc_icmphdr {
  uint8_t type; /* message type */
  uint8_t code; /* type sub-code */
  uint16_t checksum;
  union {
    struct {
      uint16_t id;
      uint16_t sequence;
    } echo; /* echo datagram */

    uint32_t gateway; /* gateway address */
    struct {
      uint16_t _unused;
      uint16_t mtu;
    } frag; /* path mtu discovery */
  } un;
} PACK_OFF;

typedef enum {
  HTTP_METHOD_UNKNOWN = 0,
  HTTP_METHOD_OPTIONS,
  HTTP_METHOD_GET,
  HTTP_METHOD_HEAD,
  HTTP_METHOD_POST,
  HTTP_METHOD_PUT,
  HTTP_METHOD_DELETE,
  HTTP_METHOD_TRACE,
  HTTP_METHOD_CONNECT
} nslc_http_method;

/************************AH header  ****************************/
PACK_ON
struct slc_ahhd {
  uint8_t next_hd;
  uint8_t length;
  uint32_t ah_spi;
  uint32_t ah_seq;
  uint8_t *ah_icv;
} PACK_OFF;

/* ++++++++++++++++++++++ ESP header +++++++++++++++++++++++++ */
PACK_ON
struct slc_esphd {
  uint32_t spi;
  uint32_t seq;
  uint32_t payloadlength;
  uint32_t paddinglength;
  uint32_t authentlength;
} PACK_OFF;

struct slc_int_one_line_struct {
  const uint8_t *ptr;
  uint16_t len;
};

#endif /* __NSLC_TYPEDEFS_H__ */
