#ifndef __PLUGIN_LOADER_H__
#define __PLUGIN_LOADER_H__

#include "../ipc/ipc.h"
#include <dlfcn.h>
#include <string>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 插件接口函数指针类型定义
 */
typedef IPC_CONFIG* (*ipc_create_config_func_t)(ipc_on_new_stream, ipc_on_new_nalu);
typedef IPC_DECODER* (*ipc_create_decoder_func_t)(IPC_CONFIG*);
typedef void (*ipc_destroy_decoder_func_t)(IPC_DECODER*);
typedef int (*ipc_process_packet_func_t)(IPC_DECODER*, const uint8_t*, int);
typedef void (*ipc_cfg_set_user_data_func_t)(IPC_CONFIG*, void*);
typedef void (*ipc_cfg_set_config_flag_func_t)(IPC_CONFIG*, uint64_t);
typedef void (*ipc_cfg_set_config_frame_type_func_t)(IPC_CONFIG*, enum IPC_FRAME_TYPE_E);
typedef void (*ipc_cfg_set_config_log_level_func_t)(IPC_CONFIG*, enum IPC_LOG_LEVEL_E);
typedef void (*ipc_cfg_set_exclude_packet_ip_func_t)(IPC_CONFIG*, uint32_t);
typedef uint64_t (*ipc_deocder_get_stream_capability_func_t)(IPC_DECODER*, uint64_t);
typedef int (*ipc_deocder_get_stream_latest_sps_func_t)(IPC_DECODER*, uint64_t, uint8_t*, size_t);
typedef int (*ipc_deocder_get_stream_latest_pps_func_t)(IPC_DECODER*, uint64_t, uint8_t*, size_t);
typedef int (*ipc_deocder_get_deviceid_func_t)(IPC_DECODER*, uint64_t, uint8_t*, size_t);

/**
 * @brief 插件接口结构体
 * 包含所有从插件动态库中加载的函数指针
 */
struct plugin_interface {
    // 核心接口
    ipc_create_config_func_t ipc_create_config;
    ipc_create_decoder_func_t ipc_create_decoder;
    ipc_destroy_decoder_func_t ipc_destroy_decoder;
    ipc_process_packet_func_t ipc_process_packet;
    
    // 配置接口
    ipc_cfg_set_user_data_func_t ipc_cfg_set_user_data;
    ipc_cfg_set_config_flag_func_t ipc_cfg_set_config_flag;
    ipc_cfg_set_config_frame_type_func_t ipc_cfg_set_config_frame_type;
    ipc_cfg_set_config_log_level_func_t ipc_cfg_set_config_log_level;
    ipc_cfg_set_exclude_packet_ip_func_t ipc_cfg_set_exclude_packet_ip;
    
    // 查询接口
    ipc_deocder_get_stream_capability_func_t ipc_deocder_get_stream_capability;
    ipc_deocder_get_stream_latest_sps_func_t ipc_deocder_get_stream_latest_sps;
    ipc_deocder_get_stream_latest_pps_func_t ipc_deocder_get_stream_latest_pps;
    ipc_deocder_get_deviceid_func_t ipc_deocder_get_deviceid;
};

/**
 * @brief 插件加载器类
 */
class PluginLoader {
public:
    PluginLoader();
    ~PluginLoader();
    
    /**
     * @brief 加载插件
     * @param plugin_path 插件文件路径
     * @return 0表示成功，-1表示失败
     */
    int load_plugin(const std::string& plugin_path);
    
    /**
     * @brief 卸载插件
     */
    void unload_plugin();
    
    /**
     * @brief 检查插件是否已加载
     * @return true表示已加载，false表示未加载
     */
    bool is_loaded() const;
    
    /**
     * @brief 获取插件接口
     * @return 插件接口指针，如果未加载则返回nullptr
     */
    const struct plugin_interface* get_interface() const;
    
    /**
     * @brief 获取插件路径
     * @return 当前加载的插件路径
     */
    const std::string& get_plugin_path() const;
    
    /**
     * @brief 获取插件版本信息
     * @return 插件版本号，如果获取失败返回0
     */
    uint8_t get_plugin_version() const;

private:
    void* plugin_handle_;                    // dlopen句柄
    struct plugin_interface interface_;     // 插件接口
    std::string plugin_path_;               // 插件路径
    bool loaded_;                           // 是否已加载
    
    /**
     * @brief 加载插件符号
     * @return 0表示成功，-1表示失败
     */
    int load_symbols();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
};

#ifdef __cplusplus
}
#endif

#endif /* __PLUGIN_LOADER_H__ */
