#include "unix_socket.h"
#include <iostream>
#include <cstring>
#include <sys/epoll.h>
#include <errno.h>
#include <fcntl.h>
#include <mutex>
#include <algorithm>

// UnixSocketServer 实现
UnixSocketServer::UnixSocketServer(const std::string& socket_path)
    : socket_path_(socket_path), server_fd_(-1), running_(false), 
      epoll_fd_(-1), sequence_counter_(0) {
}

UnixSocketServer::~UnixSocketServer() {
    stop();
}

bool UnixSocketServer::start() {
    if (running_.load()) {
        return true;
    }
    
    if (!setup_server_socket()) {
        return false;
    }
    
    // 创建epoll
    epoll_fd_ = epoll_create1(EPOLL_CLOEXEC);
    if (epoll_fd_ == -1) {
        std::cerr << "Failed to create epoll: " << strerror(errno) << std::endl;
        cleanup();
        return false;
    }
    
    // 添加服务器socket到epoll
    struct epoll_event ev;
    ev.events = EPOLLIN;
    ev.data.fd = server_fd_;
    if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, server_fd_, &ev) == -1) {
        std::cerr << "Failed to add server socket to epoll: " << strerror(errno) << std::endl;
        cleanup();
        return false;
    }
    
    running_.store(true);
    
    // 启动接受连接线程
    accept_thread_ = std::thread(&UnixSocketServer::accept_loop, this);
    
    // 启动消息处理线程
    process_thread_ = std::thread(&UnixSocketServer::process_loop, this);
    
    std::cout << "Unix Socket Server started on: " << socket_path_ << std::endl;
    return true;
}

void UnixSocketServer::stop() {
    if (!running_.load()) {
        return;
    }
    
    running_.store(false);
    
    // 等待线程结束
    if (accept_thread_.joinable()) {
        accept_thread_.join();
    }
    if (process_thread_.joinable()) {
        process_thread_.join();
    }
    
    cleanup();
    std::cout << "Unix Socket Server stopped" << std::endl;
}

void UnixSocketServer::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

bool UnixSocketServer::send_message(int client_fd, uint32_t msg_type, const uint8_t* data, uint32_t length) {
    unix_socket_msg_header header;
    header.magic = MAGIC_NUMBER;
    header.msg_type = msg_type;
    header.msg_length = length;
    header.sequence = sequence_counter_.fetch_add(1);
    
    return send_message_internal(client_fd, header, data);
}

void UnixSocketServer::broadcast_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    for (int client_fd : client_fds_) {
        send_message(client_fd, msg_type, data, length);
    }
}

size_t UnixSocketServer::get_client_count() const {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    return client_fds_.size();
}

bool UnixSocketServer::setup_server_socket() {
    // 删除已存在的socket文件
    unlink(socket_path_.c_str());
    
    // 创建socket
    server_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (server_fd_ == -1) {
        std::cerr << "Failed to create socket: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 设置socket地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);
    
    // 绑定socket
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        std::cerr << "Failed to bind socket: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }
    
    // 监听连接
    if (listen(server_fd_, 10) == -1) {
        std::cerr << "Failed to listen on socket: " << strerror(errno) << std::endl;
        close(server_fd_);
        server_fd_ = -1;
        return false;
    }
    
    return true;
}

void UnixSocketServer::cleanup() {
    // 关闭所有客户端连接
    {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        for (int client_fd : client_fds_) {
            close(client_fd);
        }
        client_fds_.clear();
    }
    
    // 关闭epoll
    if (epoll_fd_ != -1) {
        close(epoll_fd_);
        epoll_fd_ = -1;
    }
    
    // 关闭服务器socket
    if (server_fd_ != -1) {
        close(server_fd_);
        server_fd_ = -1;
    }
    
    // 删除socket文件
    unlink(socket_path_.c_str());
}

void UnixSocketServer::add_client(int client_fd) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    client_fds_.push_back(client_fd);
    std::cout << "Client connected, fd: " << client_fd << ", total clients: " << client_fds_.size() << std::endl;
}

void UnixSocketServer::remove_client(int client_fd) {
    std::lock_guard<std::mutex> lock(clients_mutex_);
    auto it = std::find(client_fds_.begin(), client_fds_.end(), client_fd);
    if (it != client_fds_.end()) {
        client_fds_.erase(it);
        close(client_fd);
        std::cout << "Client disconnected, fd: " << client_fd << ", total clients: " << client_fds_.size() << std::endl;
    }
}

void UnixSocketServer::accept_loop() {
    while (running_.load()) {
        struct sockaddr_un client_addr;
        socklen_t client_len = sizeof(client_addr);
        
        int client_fd = accept(server_fd_, (struct sockaddr*)&client_addr, &client_len);
        if (client_fd == -1) {
            if (running_.load()) {
                std::cerr << "Failed to accept connection: " << strerror(errno) << std::endl;
            }
            continue;
        }
        
        // 设置非阻塞模式
        int flags = fcntl(client_fd, F_GETFL, 0);
        fcntl(client_fd, F_SETFL, flags | O_NONBLOCK);
        
        // 添加到epoll
        struct epoll_event ev;
        ev.events = EPOLLIN | EPOLLET; // 边缘触发
        ev.data.fd = client_fd;
        if (epoll_ctl(epoll_fd_, EPOLL_CTL_ADD, client_fd, &ev) == -1) {
            std::cerr << "Failed to add client to epoll: " << strerror(errno) << std::endl;
            close(client_fd);
            continue;
        }
        
        add_client(client_fd);
    }
}

void UnixSocketServer::process_loop() {
    struct epoll_event events[MAX_EVENTS];
    
    while (running_.load()) {
        int nfds = epoll_wait(epoll_fd_, events, MAX_EVENTS, 1000); // 1秒超时
        if (nfds == -1) {
            if (errno != EINTR && running_.load()) {
                std::cerr << "epoll_wait failed: " << strerror(errno) << std::endl;
            }
            continue;
        }
        
        for (int i = 0; i < nfds; i++) {
            int fd = events[i].data.fd;
            
            if (fd == server_fd_) {
                // 新连接，由accept_loop处理
                continue;
            } else {
                // 客户端数据
                if (events[i].events & (EPOLLERR | EPOLLHUP)) {
                    remove_client(fd);
                } else if (events[i].events & EPOLLIN) {
                    if (!receive_message(fd)) {
                        remove_client(fd);
                    }
                }
            }
        }
    }
}

bool UnixSocketServer::receive_message(int client_fd) {
    uint8_t buffer[MAX_MESSAGE_SIZE];
    ssize_t bytes_read = recv(client_fd, buffer, sizeof(buffer), 0);
    
    if (bytes_read <= 0) {
        if (bytes_read == 0) {
            // 客户端关闭连接
            return false;
        } else if (errno != EAGAIN && errno != EWOULDBLOCK) {
            std::cerr << "Failed to receive message: " << strerror(errno) << std::endl;
            return false;
        }
        return true; // EAGAIN/EWOULDBLOCK是正常的
    }
    
    return parse_message(client_fd, buffer, bytes_read);
}

bool UnixSocketServer::parse_message(int client_fd, const uint8_t* buffer, size_t length) {
    if (length < sizeof(unix_socket_msg_header)) {
        std::cerr << "Message too short" << std::endl;
        return false;
    }
    
    const unix_socket_msg_header* header = reinterpret_cast<const unix_socket_msg_header*>(buffer);
    
    if (header->magic != MAGIC_NUMBER) {
        std::cerr << "Invalid magic number" << std::endl;
        return false;
    }
    
    if (header->msg_length > MAX_MESSAGE_SIZE - sizeof(unix_socket_msg_header)) {
        std::cerr << "Message too large" << std::endl;
        return false;
    }
    
    if (length < sizeof(unix_socket_msg_header) + header->msg_length) {
        std::cerr << "Incomplete message" << std::endl;
        return false;
    }
    
    const uint8_t* data = buffer + sizeof(unix_socket_msg_header);
    
    if (message_handler_) {
        message_handler_(header->msg_type, data, header->msg_length, client_fd);
    }
    
    return true;
}

bool UnixSocketServer::send_message_internal(int client_fd, const unix_socket_msg_header& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }
    
    // 发送消息体
    if (header.msg_length > 0 && data) {
        sent = send(client_fd, data, header.msg_length, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_length)) {
            return false;
        }
    }
    
    return true;
}

// UnixSocketClient 实现
UnixSocketClient::UnixSocketClient(const std::string& socket_path)
    : socket_path_(socket_path), client_fd_(-1), connected_(false),
      receiving_(false), sequence_counter_(0) {
}

UnixSocketClient::~UnixSocketClient() {
    disconnect();
}

bool UnixSocketClient::connect() {
    if (connected_.load()) {
        return true;
    }

    // 创建socket
    client_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
    if (client_fd_ == -1) {
        std::cerr << "Failed to create client socket: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置服务器地址
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path) - 1);

    // 连接到服务器
    if (::connect(client_fd_, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        std::cerr << "Failed to connect to server: " << strerror(errno) << std::endl;
        close(client_fd_);
        client_fd_ = -1;
        return false;
    }

    connected_.store(true);
    std::cout << "Connected to Unix Socket Server: " << socket_path_ << std::endl;
    return true;
}

void UnixSocketClient::disconnect() {
    if (!connected_.load()) {
        return;
    }

    stop_async_receive();

    connected_.store(false);

    if (client_fd_ != -1) {
        close(client_fd_);
        client_fd_ = -1;
    }

    std::cout << "Disconnected from Unix Socket Server" << std::endl;
}

bool UnixSocketClient::send_message(uint32_t msg_type, const uint8_t* data, uint32_t length) {
    if (!connected_.load()) {
        return false;
    }

    unix_socket_msg_header header;
    header.magic = MAGIC_NUMBER;
    header.msg_type = msg_type;
    header.msg_length = length;
    header.sequence = sequence_counter_.fetch_add(1);

    return send_message_internal(header, data);
}

bool UnixSocketClient::receive_message(uint32_t& msg_type, std::vector<uint8_t>& data, uint32_t timeout_ms) {
    if (!connected_.load()) {
        return false;
    }

    unix_socket_msg_header header;
    if (!receive_message_internal(header, data)) {
        return false;
    }

    msg_type = header.msg_type;
    return true;
}

void UnixSocketClient::set_message_handler(MessageHandler handler) {
    message_handler_ = handler;
}

void UnixSocketClient::start_async_receive() {
    if (receiving_.load() || !connected_.load()) {
        return;
    }

    receiving_.store(true);
    receive_thread_ = std::thread(&UnixSocketClient::receive_loop, this);
}

void UnixSocketClient::stop_async_receive() {
    if (!receiving_.load()) {
        return;
    }

    receiving_.store(false);

    if (receive_thread_.joinable()) {
        receive_thread_.join();
    }
}

void UnixSocketClient::receive_loop() {
    while (receiving_.load() && connected_.load()) {
        uint32_t msg_type;
        std::vector<uint8_t> data;

        if (receive_message(msg_type, data, 1000)) { // 1秒超时
            if (message_handler_) {
                message_handler_(msg_type, data.data(), data.size(), client_fd_);
            }
        }
    }
}

bool UnixSocketClient::send_message_internal(const unix_socket_msg_header& header, const uint8_t* data) {
    // 发送消息头
    ssize_t sent = send(client_fd_, &header, sizeof(header), MSG_NOSIGNAL);
    if (sent != sizeof(header)) {
        return false;
    }

    // 发送消息体
    if (header.msg_length > 0 && data) {
        sent = send(client_fd_, data, header.msg_length, MSG_NOSIGNAL);
        if (sent != static_cast<ssize_t>(header.msg_length)) {
            return false;
        }
    }

    return true;
}

bool UnixSocketClient::receive_message_internal(unix_socket_msg_header& header, std::vector<uint8_t>& data) {
    // 接收消息头
    ssize_t received = recv(client_fd_, &header, sizeof(header), 0);
    if (received != sizeof(header)) {
        return false;
    }

    if (header.magic != MAGIC_NUMBER) {
        std::cerr << "Invalid magic number in received message" << std::endl;
        return false;
    }

    if (header.msg_length > MAX_MESSAGE_SIZE) {
        std::cerr << "Message too large: " << header.msg_length << std::endl;
        return false;
    }

    // 接收消息体
    if (header.msg_length > 0) {
        data.resize(header.msg_length);
        received = recv(client_fd_, data.data(), header.msg_length, 0);
        if (received != static_cast<ssize_t>(header.msg_length)) {
            return false;
        }
    } else {
        data.clear();
    }

    return true;
}
