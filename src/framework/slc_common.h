#ifndef __SLC_COMMON_H
#define __SLC_COMMON_H
#include <stdlib.h>
#include "stdint.h"

#define _U_ __attribute__((unused))

#define SLC_MAX(a, b) ((a) > (b) ? (a) : (b))
#define SLC_MIN(a, b) ((a) < (b) ? (a) : (b))
#define get_uint8_t(X, O) (*(const uint8_t *)(((const uint8_t *)X) + O))
#define get_uint16_t(X, O) (*(const uint16_t *)(((const uint8_t *)X) + O))
#define get_uint32_t(X, O) (*(const uint32_t *)(((const uint8_t *)X) + O))
#define get_uint64_t(X, O) (*(const uint64_t *)(((const uint8_t *)X) + O))

#define get_uint16_ntohs(X, O) (ntohs(*(const uint16_t *)(((const uint8_t *)X) + O)))
#define get_uint32_ntohl(X, O) (ntohl(*(const uint32_t *)(((const uint8_t *)X) + O)))

#define ATOMIC_FETCH_ADD(a) __sync_fetch_and_add(a, 1)
#define ATOMIC_FETCH_SUB(a) __sync_fetch_and_sub(a, 1)
#define ATOMIC_ADD_FETCH(a) __sync_add_and_fetch(a, 1)
#define ATOMIC_SUB_FETCH(a) __sync_sub_and_fetch(a, 1)
#define ATOMIC_ADD_NUM(a, n) __sync_add_and_fetch(a, n)
#define ATOMIC_SUB_NUM(a, n) __sync_sub_and_fetch(a, n)
#define ATOMIC_COM_SWAP(a, o, n) __sync_val_compare_and_swap(a, o, n)
#define ATOMIC_SET(a, n) __sync_lock_test_and_set(a, n)
#define ATOMIC_ZERO(a) __sync_lock_release(a)

struct int_to_string {
  int         value;
  const char *strptr;
};

#include <common/c_lang_linkage_start.h>

struct header_value {
  uint8_t        need_free;
  uint16_t       len;
  const uint8_t *ptr;
};
struct header_tmp_value {
  uint8_t  need_free;
  uint16_t len;
  uint8_t *ptr;
};

const char *val_to_string(const int val, const struct int_to_string *vs);
int         find_packet_line_end(const uint8_t *data, uint16_t len);
int         ipv6_to_uint8(const char *addr_str, int length, unsigned char ipv6_addr_ptr[]);
int         find_special_char(const uint8_t *start, int max_len, char c);
int         find_blank_space(const uint8_t *start, int max_len);
int         find_special_colon_space(const uint8_t *start, int max_len);
char       *strdown_inplace(char *str);
static int  isUTF8(const char *pData, int len);
uint32_t    dotted_to_addr(const char *ip_string);
int         get_iparray_to_string(char *__str, int len, const uint8_t *ip);

#define PROTOCOL_HEAD_DEF(head_name)  \
  const uint8_t *head_name##_val_ptr; \
  uint32_t       head_name##_val_len;
#define PROTOCOL_VAL_PTR(head_name) head_name##_val_ptr
#define PROTOCOL_VAL_LEN(head_name) head_name##_val_len
#define array_length(x) (sizeof x / sizeof x[0])

#include <common/c_lang_linkage_end.h>

#endif
