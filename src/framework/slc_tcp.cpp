
#include "slc_tcp.h"
#include "ipc/ipc_decoder.h"
#include "slc_detect.h"

#ifdef __cplusplus
extern "C" {
#endif

int tcp_rsm_dequeue(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  auto g_RegDissectCallbackMap = getRegDissectCallbackMap();

  flow_info *flow = (flow_info *)user;
  return g_RegDissectCallbackMap[flow->real_protocol_id].dissect_rsm_func(user, C2S, ptr, len, seq, ack, flg);
}

int tcp_rsm_miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len) {
  auto g_RegDissectCallbackMap = getRegDissectCallbackMap();

  flow_info *flow = (flow_info *)user;
  return g_RegDissect<PERSON>allbackMap[flow->real_protocol_id].dissect_miss_func(user, C2<PERSON>, miss_seq, miss_len);
}

#ifdef __cplusplus
}
#endif

/********** TCP_RSM 是一个纯粹的库 *************************/
/********** TCP_RSM 中间层 *********************************/

uint64_t tcp_alloc_cnt = 0;
uint64_t tcp_free_cnt = 0;

int flow_tcp_rsm(flow_info *flow, const struct slc_tcphdr *tcph, const unsigned char *payload, int payload_len) {
  uint32_t fail = 0;
  uint32_t tcp_seq = ntohl(tcph->seq);
  uint32_t tcp_ack = ntohl(tcph->ack_seq);
  uint16_t portsrc = ntohs(tcph->source);
  uint16_t portdst = ntohs(tcph->dest);
  uint8_t  C2S = portsrc > portdst;
  uint8_t  flg = 0;

  flg |= tcph->fin << 0;
  flg |= tcph->syn << 1;
  flg |= tcph->rst << 2;
  flg |= tcph->psh << 3;

  auto g_RegDissectCallbackMap = getRegDissectCallbackMap();

  ipc_decoder *decoder = static_cast<ipc_decoder *>(flow->user);
  //初始化
  if (NULL == flow->rsm) {
    flow->rsm = tcp_rsm_init(tcp_rsm_dequeue, tcp_rsm_miss, flow, decoder->getTcpRsmOutOfOrder());
    if (flow->cache_num) {
      for (int i = 0; i < flow->cache_num; i++) {
        tcp_rsm_push(flow->rsm, flow->cache[i].c2s, NULL, 0, flow->cache[i].seq, flow->cache[i].ack_seq, flow->cache[i].flag);
      }
    } else {
      uint8_t fk_flg;
      fk_flg |= 0 << 0;
      fk_flg |= 1 << 1;
      fk_flg |= 0 << 2;
      fk_flg |= 0 << 3;
      fk_flg |= 1 << 5;
      tcp_rsm_push(flow->rsm, !C2S, NULL, 0, tcp_ack - 1, 0, fk_flg);
      tcp_rsm_push(flow->rsm, C2S, NULL, 0, tcp_seq - 1, 0, fk_flg);
    }
  }

  //加入重组
  int rc = tcp_rsm_push(flow->rsm, C2S, payload, payload_len, tcp_seq, tcp_ack, flg);
  if (rc < 0) {
    fail = -1;
  }

  return fail;
}
