/**
 * @file wxcs_observer.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 实现观察者模式的头文件
 * @version 0.1
 * @date 2023-01-28
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#include <functional>
#include <map>
#include <mutex>
#include "tcp_rsm/tcp_rsm.h"
#include "slc_logger.h"
#include "slc_flow.h"

#ifndef _WXCS_OBSERVER_H
#define _WXCS_OBSERVER_H

extern slc::Logger global_logger;

typedef std::function<void(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)> RegCallback;
std::map<int, FuncPair> &getRegDissectCallbackMap();
typedef std::function<int(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg)>
                                                                                          RegRsmDequeCallback;
typedef std::function<int(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len)> RegRsmMissCallback;
/**
 * @brief 模块通过继承该类，实现了观察者模式中的观察者，通过setRegCallback设置消息到达时进入的解析函数
 *        并且模块需要重写onTimeout函数，来实现模块本身的超时
 * 
 */
class ObserverSessionKeeper {
public:
  // virtual void onTimeout() = 0;

  virtual void setRegCallback(int msgtype, const char* module_name,const RegCallback &identify_cb, const RegCallback &dissect_cb,
      const RegRsmDequeCallback &dissect_rsm_cb, const RegRsmMissCallback &dissect_miss_cb) {
    std::lock_guard<std::mutex> lck(MapMtx);

    auto &g_RegDissectCallbackMap = getRegDissectCallbackMap();

    auto it = g_RegDissectCallbackMap.find(msgtype);
    if (it == g_RegDissectCallbackMap.end()) {
      FuncPair funcpair;
      funcpair.identify_func = identify_cb;
      funcpair.dissect_func = dissect_cb;
      funcpair.dissect_rsm_func = dissect_rsm_cb;
      funcpair.dissect_miss_func = dissect_miss_cb;

      g_RegDissectCallbackMap.emplace(msgtype, funcpair);
      global_logger.Info("module is Reg", module_name);
      return;
    } else {
      global_logger.Error("msgtype already Reg", module_name);
      return;
    }
  }

  ~ObserverSessionKeeper() {}

private:
  std::mutex MapMtx;
};

#endif