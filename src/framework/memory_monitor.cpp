#include "memory_monitor.h"
#include <iostream>
#include <iomanip>
#include <fstream>
#include <sstream>

MemoryMonitor& MemoryMonitor::getInstance() {
    static MemoryMonitor instance;
    return instance;
}

void MemoryMonitor::recordAllocation(size_t size) {
    current_usage_.fetch_add(size);
    allocation_count_.fetch_add(1);
    
    // 更新峰值使用量
    size_t current = current_usage_.load();
    size_t peak = peak_usage_.load();
    while (current > peak && !peak_usage_.compare_exchange_weak(peak, current)) {
        peak = peak_usage_.load();
    }
}

void MemoryMonitor::recordDeallocation(size_t size) {
    current_usage_.fetch_sub(size);
    deallocation_count_.fetch_add(1);
}

size_t MemoryMonitor::getCurrentUsage() const {
    return current_usage_.load();
}

size_t MemoryMonitor::getPeakUsage() const {
    return peak_usage_.load();
}

uint64_t MemoryMonitor::getAllocationCount() const {
    return allocation_count_.load();
}

uint64_t MemoryMonitor::getDeallocationCount() const {
    return deallocation_count_.load();
}

void MemoryMonitor::reset() {
    current_usage_.store(0);
    peak_usage_.store(0);
    allocation_count_.store(0);
    deallocation_count_.store(0);
}

void MemoryMonitor::printReport() const {
    size_t current = getCurrentUsage();
    size_t peak = getPeakUsage();
    uint64_t allocs = getAllocationCount();
    uint64_t deallocs = getDeallocationCount();
    
    std::cout << "\n=== Memory Usage Report ===" << std::endl;
    std::cout << "Current Usage: " << formatBytes(current) << std::endl;
    std::cout << "Peak Usage:    " << formatBytes(peak) << std::endl;
    std::cout << "Allocations:   " << allocs << std::endl;
    std::cout << "Deallocations: " << deallocs << std::endl;
    std::cout << "Pending:       " << (allocs - deallocs) << std::endl;
    
    if (hasMemoryLeak()) {
        std::cout << "WARNING: Potential memory leak detected!" << std::endl;
    }
    
    // 获取系统内存信息
    printSystemMemoryInfo();
    std::cout << "=========================" << std::endl;
}

bool MemoryMonitor::hasMemoryLeak() const {
    return getCurrentUsage() > 0 || getAllocationCount() != getDeallocationCount();
}

std::string MemoryMonitor::formatBytes(size_t bytes) const {
    const char* units[] = {"B", "KB", "MB", "GB"};
    int unit = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit < 3) {
        size /= 1024.0;
        unit++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return oss.str();
}

void MemoryMonitor::printSystemMemoryInfo() const {
    std::ifstream meminfo("/proc/meminfo");
    if (!meminfo.is_open()) {
        std::cout << "System Memory: Unable to read /proc/meminfo" << std::endl;
        return;
    }
    
    std::string line;
    size_t total_mem = 0, free_mem = 0, available_mem = 0;
    
    while (std::getline(meminfo, line)) {
        if (line.find("MemTotal:") == 0) {
            std::istringstream iss(line);
            std::string label, unit;
            iss >> label >> total_mem >> unit;
            total_mem *= 1024; // Convert from KB to bytes
        } else if (line.find("MemFree:") == 0) {
            std::istringstream iss(line);
            std::string label, unit;
            iss >> label >> free_mem >> unit;
            free_mem *= 1024; // Convert from KB to bytes
        } else if (line.find("MemAvailable:") == 0) {
            std::istringstream iss(line);
            std::string label, unit;
            iss >> label >> available_mem >> unit;
            available_mem *= 1024; // Convert from KB to bytes
        }
    }
    
    if (total_mem > 0) {
        size_t used_mem = total_mem - free_mem;
        double usage_percent = (static_cast<double>(used_mem) / total_mem) * 100.0;
        
        std::cout << "System Memory:" << std::endl;
        std::cout << "  Total:     " << formatBytes(total_mem) << std::endl;
        std::cout << "  Used:      " << formatBytes(used_mem) << " (" 
                  << std::fixed << std::setprecision(1) << usage_percent << "%)" << std::endl;
        std::cout << "  Free:      " << formatBytes(free_mem) << std::endl;
        if (available_mem > 0) {
            std::cout << "  Available: " << formatBytes(available_mem) << std::endl;
        }
    }
}

// 全局内存监控函数
void* monitored_malloc(size_t size) {
    void* ptr = malloc(size);
    if (ptr) {
        MemoryMonitor::getInstance().recordAllocation(size);
    }
    return ptr;
}

void monitored_free(void* ptr, size_t size) {
    if (ptr) {
        free(ptr);
        MemoryMonitor::getInstance().recordDeallocation(size);
    }
}

// 重载new和delete操作符（可选）
#ifdef ENABLE_GLOBAL_MEMORY_MONITORING
void* operator new(size_t size) {
    void* ptr = malloc(size);
    if (ptr) {
        MemoryMonitor::getInstance().recordAllocation(size);
    }
    return ptr;
}

void operator delete(void* ptr) noexcept {
    if (ptr) {
        // 注意：这里无法获取原始大小，这是一个限制
        free(ptr);
        // MemoryMonitor::getInstance().recordDeallocation(size); // 无法获取size
    }
}

void* operator new[](size_t size) {
    void* ptr = malloc(size);
    if (ptr) {
        MemoryMonitor::getInstance().recordAllocation(size);
    }
    return ptr;
}

void operator delete[](void* ptr) noexcept {
    if (ptr) {
        free(ptr);
        // MemoryMonitor::getInstance().recordDeallocation(size); // 无法获取size
    }
}
#endif
