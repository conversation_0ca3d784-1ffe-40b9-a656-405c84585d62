/**
 * @file wxcs_singleton.h
 * <AUTHOR> name (<EMAIL>)
 * @brief 单例模式的实现
 * @version 0.1
 * @date 2023-01-28
 * 
 * @copyright Copyright (c) 2023
 * 
 */
#ifndef _SLC_SINGLETON_H
#define _SLC_SINGLETON_H

#include <memory>
/**
 * @brief 单例模板类
 *        模块通过继承singleton基类，实现单例
 * @tparam SingletonKeeper 
 */
template <typename SingletonKeeper>
class singleton {
public:
  singleton(const singleton &) = delete;
  singleton(singleton &&) = delete;
  singleton &operator=(const singleton &) = delete;
  singleton &operator=(singleton &&) = delete;

  static std::shared_ptr<SingletonKeeper> &GetInstance() {
    static std::shared_ptr<SingletonKeeper> keeper(new SingletonKeeper);
    return keeper;
  }

public:
  singleton() = default;
  virtual ~singleton() = default;
};
#endif