#ifndef __MEMORY_MONITOR_H__
#define __MEMORY_MONITOR_H__

#include <cstdint>
#include <atomic>
#include <string>

class MemoryMonitor {
public:
    static MemoryMonitor& getInstance();
    
    // 记录内存分配
    void recordAllocation(size_t size);
    
    // 记录内存释放
    void recordDeallocation(size_t size);
    
    // 获取当前内存使用量
    size_t getCurrentUsage() const;
    
    // 获取峰值内存使用量
    size_t getPeakUsage() const;
    
    // 获取总分配次数
    uint64_t getAllocationCount() const;
    
    // 获取总释放次数
    uint64_t getDeallocationCount() const;
    
    // 重置统计信息
    void reset();
    
    // 打印内存使用报告
    void printReport() const;
    
    // 检查是否有内存泄漏
    bool hasMemoryLeak() const;

    // 格式化字节数显示
    std::string formatBytes(size_t bytes) const;

    // 打印系统内存信息
    void printSystemMemoryInfo() const;

private:
    MemoryMonitor() = default;
    ~MemoryMonitor() = default;
    MemoryMonitor(const MemoryMonitor&) = delete;
    MemoryMonitor& operator=(const MemoryMonitor&) = delete;
    
    std::atomic<size_t> current_usage_{0};
    std::atomic<size_t> peak_usage_{0};
    std::atomic<uint64_t> allocation_count_{0};
    std::atomic<uint64_t> deallocation_count_{0};
};

// 内存分配包装器
template<typename T>
class MemoryWrapper {
public:
    static T* allocate(size_t count = 1) {
        size_t size = sizeof(T) * count;
        T* ptr = new T[count];
        MemoryMonitor::getInstance().recordAllocation(size);
        return ptr;
    }
    
    static void deallocate(T* ptr, size_t count = 1) {
        if (ptr) {
            size_t size = sizeof(T) * count;
            delete[] ptr;
            MemoryMonitor::getInstance().recordDeallocation(size);
        }
    }
};

// 智能指针包装器
template<typename T>
class MonitoredUniquePtr {
public:
    MonitoredUniquePtr() : ptr_(nullptr), size_(0) {}
    
    explicit MonitoredUniquePtr(size_t count) : size_(sizeof(T) * count) {
        ptr_ = new T[count];
        MemoryMonitor::getInstance().recordAllocation(size_);
    }
    
    ~MonitoredUniquePtr() {
        if (ptr_) {
            delete[] ptr_;
            MemoryMonitor::getInstance().recordDeallocation(size_);
        }
    }
    
    // 移动构造函数
    MonitoredUniquePtr(MonitoredUniquePtr&& other) noexcept 
        : ptr_(other.ptr_), size_(other.size_) {
        other.ptr_ = nullptr;
        other.size_ = 0;
    }
    
    // 移动赋值操作符
    MonitoredUniquePtr& operator=(MonitoredUniquePtr&& other) noexcept {
        if (this != &other) {
            if (ptr_) {
                delete[] ptr_;
                MemoryMonitor::getInstance().recordDeallocation(size_);
            }
            ptr_ = other.ptr_;
            size_ = other.size_;
            other.ptr_ = nullptr;
            other.size_ = 0;
        }
        return *this;
    }
    
    // 禁用拷贝
    MonitoredUniquePtr(const MonitoredUniquePtr&) = delete;
    MonitoredUniquePtr& operator=(const MonitoredUniquePtr&) = delete;
    
    T* get() const { return ptr_; }
    T& operator*() const { return *ptr_; }
    T* operator->() const { return ptr_; }
    T& operator[](size_t index) const { return ptr_[index]; }
    
    explicit operator bool() const { return ptr_ != nullptr; }
    
    void reset() {
        if (ptr_) {
            delete[] ptr_;
            MemoryMonitor::getInstance().recordDeallocation(size_);
            ptr_ = nullptr;
            size_ = 0;
        }
    }

private:
    T* ptr_;
    size_t size_;
};

#endif // __MEMORY_MONITOR_H__
