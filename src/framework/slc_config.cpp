/****************************************************************************************
 * 文 件 名 : slc_config.cpp
 * 项目名称 : YVBD1207001B
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 设    计 : root      '2018-03-17
* 编    码 : root      '2018-03-17
* 修    改 :
****************************************************************************************
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
***************************************************************************************/

#include "common/tools.h"
#include "slc_config.h"

SlcConfig *SlcConfig::GetInstance() {
  std::string strConfigFilePath = getAppDir() + SLC_CONF_PATH;
  if ((access(strConfigFilePath.c_str(), F_OK)) != -1) {
    static SlcConfig s_config(strConfigFilePath);
    if (-1 == s_config.parse()) {
      printf("parse config file error, please check it.");
      return NULL;
    }
    return &s_config;
  } else {
    static SlcConfig s_config;
    return &s_config;
  }
}

int SlcConfig::parse() {
  char *pStrValue = NULL;
  int   len = 0;

  lWorkMode_ = iniparser_getint(ini_, ":MODE", 0);

  pStrValue = (char *)iniparser_getstring(ini_, ":IF", "enp5s0f0");
  strIfName_ = pStrValue;

  pStrValue = (char *)iniparser_getstring(ini_, ":FILTER", "");
  strSlcCapFilter = pStrValue;

  pStrValue = (char *)iniparser_getstring(ini_, ":PCAP_FILES_DIR", "./pcaps");
  len = strlen(pStrValue);
  if (pStrValue[len - 1] == '/') {
    pStrValue[len - 1] = '\0';
  }
  strPcapFileDir_ = pStrValue;

  pStrValue = (char *)iniparser_getstring(ini_, ":RTSP_SERVER_IP", "127.0.0.1");
  excludeIP_ = ntohl(inet_addr(pStrValue));

  rtpTransferMode_ = iniparser_getint(ini_, ":RTP_TRANSFER_MODE", 0);

  cameraRtspPort_ = iniparser_getint(ini_, ":CANMERA_RTSP_PORT", 554);

  return 0;
}
const std::string SlcConfig::toGlobalConfigName(const std::string &strConfigName) { return ":" + strConfigName; }

template <>
int SlcConfig::GetValueOf_inner(const std::string &strConfigName, int def) {
  return iniparser_getint(ini_, strConfigName.c_str(), def);
}

template <>
long SlcConfig::GetValueOf_inner(const std::string &strConfigName, long def) {
  return iniparser_getlongint(ini_, strConfigName.c_str(), def);
}

template <>
CSTR SlcConfig::GetValueOf_inner(const std::string &strConfigName, CSTR def) {
  return iniparser_getstring(ini_, strConfigName.c_str(), def);
}

template <>
bool SlcConfig::GetValueOf_inner(const std::string &strConfigName, bool b) {
  return iniparser_getboolean(ini_, strConfigName.c_str(), b) == 1;
}

template <>
std::string SlcConfig::GetValueOf_inner(const std::string &strConfigName, std::string def) {
  return iniparser_getstring(ini_, strConfigName.c_str(), def.c_str());
}
