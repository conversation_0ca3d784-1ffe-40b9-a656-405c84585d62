#include "slc_common.h"
#include <netinet/in.h>
#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include <math.h>  // 编译需要添加-lm

/* Tries to match val against each element in the value_string array vs.
   Returns the associated string ptr, and sets "*idx" to the index in
   that table, on a match, and returns NULL, and sets "*idx" to -1,
   on failure. */
const char *val_to_string(const int val, const struct int_to_string *vs) {
  int i = 0;

  if (vs) {
    while (vs[i].strptr) {
      if (vs[i].value == val) {
        return (vs[i].strptr);
      }
      i++;
    }
  }

  return NULL;
}

int find_packet_line_end(const uint8_t *data, uint16_t len) {
  uint16_t i;

  for (int i = 0; i < len - 1; i++) {
    if (get_uint16_t(data, i) == ntohs(0x0d0a))
      return i;
  }

  return -1;
}

char *string_white_space_trim(char *str) {
  /* 移除字符串中空格 */
  int index;
  int new_index;
  int str_length;

  str_length = strlen(str);

  for (index = 0, new_index = 0; index < str_length; index++) {
    if (!isspace((unsigned char)str[index])) {
      str[new_index] = str[index];
      new_index++;
    }
  }

  str[new_index] = '\0';

  return str;
}

int string_char_count(const char *string, char character) {
  /* 计算字符串中，给定字符的数量 */
  int i;
  int str_length;
  int count = 0;

  str_length = strlen(string);
  for (i = 0; i < str_length; i++)
    if (string[i] == character)
      count++;

  return count;
}

//ipv4地址转换
int ipv4_to_i(const char *ip, unsigned int *ipv4_addr) {
  char         str_ip_index[4] = {'\0'};
  unsigned int ip_int, ip_add = 0;
  unsigned int j = 0, a = 3, i = 0;

  for (i = 0; i <= strlen(ip); i++) {
    if (ip[i] == '\0' || ip[i] == '.') {
      ip_int = atoi(str_ip_index);
      if (ip_int > 255)
        return 0;

      ip_add += (ip_int * pow(256, a));
      a--;
      memset(str_ip_index, 0, sizeof(str_ip_index));

      j = 0;
      continue;
    }
    if (j > 3) {
      return 0;
    }
    str_ip_index[j] = ip[i];
    j++;
  }

  *ipv4_addr = ip_add;

  return 1;
}

int ipv6_address_field_type_get(const char *field_str) {
  /* 判断ipv6地址域类型                           */
  int          i = 0;
  int          length;
  int          type;
  unsigned int ipv4_addr;

  /* 通过长度判断          */
  /* 16进制数字域： 1-4    */
  /* "::"域：0             */
  /* ipv4地址域： 7-15     */

  length = strlen(field_str);

  if (0 == length) {
    type = 1;
  } else if (length <= 4) {
    // 确保每个数字为16进制
    for (i = 0; i < length; i++)
      if (!isxdigit((unsigned char)field_str[i]))
        return -1;
    type = 0;
  } else if ((length >= 7) && (length <= 15)) {
    //确保是有效的ipv4地址
    if (ipv4_to_i(field_str, &ipv4_addr))
      type = 2;
    else
      type = -1;
  } else {
    type = -1;
  }

  return type;
}

int ipv6_to_uint8(const char *addr_str, int length, unsigned char ipv6_addr_ptr[]) {
  /***************************************************************************/
  /* 功能：解析ipv6地址字符串，转换为无符号整形,存入4个无符号整形的一维数组  */
  /* ipv6地址 128位，prefix length:                                          */
  /*                         - 64 for EUI-64 addresses                       */
  /*                         - 128 for non-EUI-64 addresses                  */
  /* 输入：ipv6地址字符串，地址位数，默认为128位                             */
  /* 输出：返回解析成功或失败;指向4个无符号整形的一维数组的指针              */
  /****************************************************************************/

  char addr_str_copy[256];
  int  i, num_fields;
  //unsigned int *ret_addr_ptr;
  unsigned short int addr_field_arr[8];
  int                addr_index;
  char              *ith_field;       // 指向地址当前域
  int                ith_field_type;  // 地址域类型
  char              *next_field;
  int                double_colon_field_index = -1;  // 字符串地址中"::"的位置
  unsigned int       ipv4_address;                   // ipv6地址中的ipv4部分
  unsigned int       msw, lsw;
  int                error = 0;

  //复制一份，以便操作
  strcpy(addr_str_copy, addr_str);

  // 移除字符串中的空格字符
  string_white_space_trim(addr_str_copy);

  // 计算字符串中冒号，字符串中地址域数比冒号多一个
  num_fields = string_char_count(addr_str_copy, ':') + 1;
  // 域最大数量为length/16 + 2
  // 如  ::0:0:0:0:0:0:0:0.
  if (num_fields > ((length >> 4) + 2)) {
    ipv6_addr_ptr = NULL;
    return 0;
  }

  // 初始化
  ith_field = addr_str_copy;
  for (i = 0, addr_index = 0; i < num_fields; i++) {
    // 获得下一个域的指针
    next_field = strchr(ith_field, ':');

    /* 若当前是最后一个域, next_field 是 NULL                       */
    /* 否则，替换':'为'\0', 字符串可以结束，从而ith_field指向当前域   */
    /* next_field指向下一个域头部                                    */
    if (NULL != next_field) {
      *next_field = '\0';
      ++next_field;
    }

    // 发现这个域的类型
    ith_field_type = ipv6_address_field_type_get(ith_field);

    switch (ith_field_type) {
      case 0:
        // 域类型为16进制表示

        if (addr_index >= (length >> 4)) {
          error = 1;
          break;
        }
        // 字符串转换为16进制
        addr_field_arr[addr_index] = (unsigned short)strtoul(ith_field, NULL, 16);
        ++addr_index;
        break;

      case 1:
        // 域类型为 "::"

        // 若出现在字符串的开头或结尾，忽略
        if ((0 == i) || (i == num_fields - 1)) {
          break;
        }

        // 若出现大于一次，错误
        if (double_colon_field_index != -1) {
          error = 1;
          break;
        }

        // 记下位置
        double_colon_field_index = addr_index;

        break;

      case 2:
        // 域类型为ipv4地址

        // 确保在地址中还有两个未设置的域
        if (addr_index >= 7) {
          error = 1;
          break;
        }

        // ipv4地址解析
        ipv4_to_i(ith_field, &ipv4_address);

        // 存储高16位
        addr_field_arr[addr_index] = (unsigned short)(ipv4_address >> 16);

        // 存储低16位
        addr_field_arr[addr_index + 1] = (unsigned short)(ipv4_address & 0x0000ffff);

        addr_index += 2;

        break;
      default:
        error = 1;
        break;
    }

    if (error) {
      ipv6_addr_ptr = NULL;
      return 0;
    }

    ith_field = next_field;
  }

  // 计算的域不是8，并且没有"::",错误
  if ((addr_index != (length >> 4)) && (-1 == double_colon_field_index)) {
    ipv6_addr_ptr = NULL;
    return 0;
  }

  if ((addr_index != (length >> 4)) && (-1 != double_colon_field_index)) {
    // 设置相应"::"对应addr_field_arr中位置为0
    memmove(addr_field_arr + (double_colon_field_index + (length >> 4) - addr_index), addr_field_arr + double_colon_field_index,
        (addr_index - double_colon_field_index) * 2);
    memset(addr_field_arr + double_colon_field_index, 0, ((length >> 4) - addr_index) * 2);
  }

  for (i = 0; i < 8; i++) {
    msw = (addr_field_arr[i] >> 8) & 0xFFFF;
    lsw = (addr_field_arr[i]) & 0xFFFF;

    (ipv6_addr_ptr)[i * 2] = msw;
    (ipv6_addr_ptr)[i * 2 + 1] = lsw;
  }

  return 1;
}

int find_special_char(const uint8_t *start, int max_len, char c) {
  int index;

  for (index = 0; index < max_len; index++) {
    if (start[index] == c)
      return index;
  }

  return -1;
}

int find_blank_space(const uint8_t *start, int max_len) {
  int index;

  for (index = 0; index < max_len; index++) {
    if (start[index] == ' ')
      return index;
  }

  return -1;
}

int find_special_colon_space(const uint8_t *start, int max_len) {
  int index;

  for (index = 0; index <= max_len - 2; index++) {
    if (memcmp(start + index, ": ", 2) == 0)
      return index;
  }

  return -1;
}

/* Convert all ASCII letters to lower case, in place. */
char *strdown_inplace(char *str) {
  char *s;

  for (s = str; *s; s++) /* What 'g_ascii_tolower (gchar c)' does, this should be slightly more efficient */
    *s = isupper(*s) ? *s - 'A' + 'a' : *s;

  return (str);
}

static int isUTF8(const char *pData, int len) {
  int         loop = len;
  const char *p = pData;

  if (NULL == pData || len <= 0) {
    return 0;
  }

  while (loop > 0) {
    if ('\r' == *p || '\n' == *p || '\t' == *p || '\b' == *p || '\f' == *p || '\v' == *p) {
      p++;
      loop--;
      continue;
    } else if (isprint(*p) > 0) {
      p++;
      loop--;
      continue;
    } else if (loop >= 2 && (0XC0 == (p[0] & 0XE0)) && (0X80 == (p[1] & 0XC0))) {
      p = p + 2;
      loop = loop - 2;
      continue;
    } else if (loop >= 3 && (0XE0 == (p[0] & 0XF0)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0))) {
      p = p + 3;
      loop = loop - 3;
      continue;
    } else if (loop >= 4 && (0XF0 == (p[0] & 0XF8)) && (0X80 == (p[1] & 0XC0)) && (0X80 == (p[2] & 0XC0)) &&
               (0X80 == (p[3] & 0XC0))) {
      p = p + 4;
      loop = loop - 4;
      continue;
    }
    return 0; /* 这不是 UTF-8 编码*/
  }
  return p - pData; /* 全部检查结束， 这是 UTF-8 编码 */
}

uint32_t dotted_to_addr(const char *ip_string) {
  int addr[4];

  if (sscanf(ip_string, "%d.%d.%d.%d", addr + 0, addr + 1, addr + 2, addr + 3) != 4) {
    return 0;
  }

  if (addr[0] < 0 || addr[1] < 0 || addr[2] < 0 || addr[3] < 0 || addr[0] > 255 || addr[1] > 255 || addr[2] > 255 ||
      addr[3] > 255) {
    return 0;
  }

  uint32_t a = (addr[0] << 24) | (addr[1] << 16) | (addr[2] << 8) | addr[3];

  // ### perhaps do gethostbyaddr here?

  a = htonl(a);

  return a;
}

int get_iparray_to_string(char *__str, int len, const uint8_t* ip)
{
    if (ip==NULL || ip+1==NULL || ip+2==NULL || ip+3==NULL) {return 0;}
    snprintf(__str, len, "%u.%u.%u.%u", ip[0], ip[1], ip[2], ip[3]);

    return 0;
}
