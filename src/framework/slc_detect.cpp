#include <cstring>
#include <netinet/in.h>
#include <sys/types.h>
#include <algorithm>
#include "memory.h"

#include <common/c_lang_linkage_start.h>
#include <string.h>
#include <common/c_lang_linkage_end.h>

#include "slc_hdlc.h"
#include "slc_config.h"
#include "slc_detect.h"
#include "slc_rtp.h"
#include "slc_typedefs.h"
#include "slc_common.h"
#include "ipc/ipc_decoder.h"
#include "ipc/ipc_config.h"
#include "slc_tcp.h"


SlcParser *SlcParser::GetInstance() {
  static SlcParser Parser;

  return &Parser;
}

flow_info *SlcParser::findCreateFlow(five_tuple *key) {
  std::string ip_dst((char *)&key->ip_dst, 16);
  std::string ip_src((char *)&key->ip_src, 16);

  Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t> tuple_key(
      ip_dst, ip_src, key->port_src, key->port_dst, key->ip_version, key->proto);
  auto it = flow_map.find(tuple_key);
  if (it == flow_map.end()) {
    flow_info flow_info;
    memset(&flow_info, 0, sizeof flow_info);
    memcpy(&flow_info.tuple, key, sizeof(five_tuple));
    flow_map[tuple_key] = flow_info;
  }
  return &flow_map[tuple_key];
}

void SlcParser::destoryFlow(five_tuple *key) {
  std::string ip_dst((char *)&key->ip_dst, 16);
  std::string ip_src((char *)&key->ip_src, 16);

  Node<std::string, std::string, uint16_t, uint16_t, uint8_t, uint8_t> tuple_key(
      ip_dst, ip_src, key->port_src, key->port_dst, key->ip_version, key->proto);
  auto it = flow_map.find(tuple_key);
  if (it != flow_map.end()) {
    flow_map.erase(it);
  }
}
int SlcParser::parser(const uint8_t *pPktData, int len, void *user) {
  ipc_decoder *decoder = static_cast<ipc_decoder *> (user);
  ipc_cfg *config = decoder->getDecoderConfig();
  const u_char *packet = pPktData;
  int           pkt_len = len;
  /* Declare pointers to packet headers */
  const struct slc_ethhdr     *ethhdr = NULL;
  const struct slc_arm_ethhdr *arm_ethhdr = NULL;
  const struct slc_iphdr      *iph = NULL;
  const struct slc_ipv6hdr    *iph6 = NULL;
  uint16_t                     ip_offset = 0;
  uint16_t                     eth_offset = 0;
  uint16_t                     ip_len = 0;
  uint16_t                     type;
  uint8_t                      proto;
  uint16_t                     check = 0;

  int             ret;
  struct pkt_info pkt_data;
  memset(&pkt_data, 0, sizeof(struct pkt_info));

  if (slc_is_chdlc_header(packet, pkt_len) == 1) {
    const struct slc_chdlc *chdlc = (const struct slc_chdlc *)packet;
    type = ntohs(chdlc->proto_code);
    if (type == 0xfefe) {
      ip_offset = 5;
    } else {
      ip_offset = 4;
    }
  }

eth_again:
  if (config->type == IPC_NETWORK_ARM) {
    arm_ethhdr = (const struct slc_arm_ethhdr *)&packet[eth_offset];
    ip_offset = sizeof(slc_arm_ethhdr) + eth_offset;
    check = ntohs(arm_ethhdr->h_proto);

  } else {
    ethhdr = (const struct slc_ethhdr *)&packet[eth_offset];
    ip_offset = sizeof(slc_ethhdr) + eth_offset;
    check = ntohs(ethhdr->h_proto);
  }

  if (check >= 0x0600) {
    type = check;
  } else if (check <= 1500) {
    return PKT_OK;
  } else {
    type = 0;
  }

//剥洋葱-手艺
strip_again:
  /*数据链路层类型*/

  switch (type) {
    case MPLS_PWETHCW:
      {
        packet += ip_offset;
        pkt_len -= ip_offset;
        eth_offset = 0;
        goto eth_again;
      }
    case VLAN:
      if (pkt_len > (uint32_t)(ip_offset + 2)) {
        return PKT_DROP;
      }
      type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
      ip_offset += 4;
      if (type == 0x8100) { /* double tagging for 802.1Q */
        type = (packet[ip_offset + 2] << 8) + packet[ip_offset + 3];
        ip_offset += 4;
      }
      goto strip_again;  //解决VLAN 中的ARP
    case MPLS_UNI:
    case MPLS_MULTI:
      return PKT_DROP;

      ip_offset += ret;
      goto strip_again;
      break;
    case PPPoE:
      //pppoe  session报文且是ipv4报文
      if (type == PPPoE && ((packet[ip_offset + 6] << 8) + packet[ip_offset + 7]) == 0x0021) {
        type = ETH_P_IP;
        ip_offset += 8;
        break;
      }
      //packet中跳过pppoe相关字段共6个字节
      return PKT_DROP;
      break;
    case ARP:
    case LACP:
    case LLDP:
    case PPPoE_DISCOVERY:
      return PKT_DROP;
    case PPP_IP:
    case PPP_IPCP:
    case PPP_LCP:
    case PPP_PAP:
    case PPP_CHAP:
      //从以太帧中的type开始
      return PKT_DROP;
      break;
    case 0x22f4:  // ISIS
    case 0xfefe:
      return PKT_OK;
    case VMLAB:
      type = get_uint16_ntohs(packet + ip_offset, VMLAB_ENCPTYPE_OFFSET);
      ip_offset += VMLAB_SIZE;
      goto strip_again;
    default:
      break;
  }

  /*追加网络层协议栈*/
  if (type != ETH_P_IP && type != ETH_P_IPV6) {
    return PKT_DROP;
  }

  iph = (const struct slc_iphdr *)&packet[ip_offset];
  pkt_data.ipversion = iph->version;
  if (iph->version == 4) {
    ip_len = ((uint16_t)iph->ihl * 4);
    proto = iph->protocol;
    iph6 = NULL;
    pkt_data.iph = iph;
    uint32_t combined_ip =
        (uint32_t)iph->saddr[0] << 24 | (uint32_t)iph->saddr[1] << 16 | (uint32_t)iph->saddr[2] << 8 | (uint32_t)iph->saddr[3];

    uint32_t camera_ip = config->exclude_packet_ip;
    uint8_t *allowNet = (uint8_t *)&camera_ip;
    uint8_t *packetNet = (uint8_t *)&combined_ip;

    // 仅允许符合配置的网段的 ip 进行下一步解析
    if (memcmp(allowNet , packetNet , 4) == 0) {
      return PKT_DROP;
    }
  } else if (iph->version == 6) {
    iph6 = (const struct slc_ipv6hdr *)&packet[ip_offset];
    proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
    ip_len = sizeof(struct slc_ipv6hdr);
    iph = NULL;
    if (proto == IPPROTO_DSTOPTS /* IPv6 destination option */) {
      const uint8_t *options = (const uint8_t *)&packet[ip_offset + ip_len];
      proto = options[0];
      ip_len += 8 * (options[1] + 1);
    }
    pkt_data.iph6 = iph6;
  } else {
    return PKT_DROP;
  }

  /*udp的数据可能是gtp，如果是gtp数据，剥离出内层ip头*/
  if (proto == IPPROTO_UDP) {
    const struct slc_udphdr *udp = (const struct slc_udphdr *)&packet[ip_offset + ip_len];
    pkt_data.udph = udp;
  }

  pkt_data.proto = proto;
  if (proto == IPPROTO_TCP) {
    const struct slc_tcphdr *tcp = (const struct slc_tcphdr *)&packet[ip_offset + ip_len];
    pkt_data.tcph = tcp;
  }

  parserIpLayer(&pkt_data, iph ? (const void *)iph : (const void *)iph6, pkt_len - ip_offset, user);
  return PKT_OK;
}

int SlcParser::parserIpLayer(struct pkt_info *pkt, const void *ip4_or_ip6, uint16_t ipsize, void *user) {
  const struct slc_tcphdr  *tcph = NULL;
  const struct slc_udphdr  *udph = NULL;
  const struct slc_iphdr   *iph = NULL;
  const struct slc_ipv6hdr *iph6 = NULL;
  const uint8_t            *l4 = NULL;
  const uint8_t            *payload = NULL;

  const uint8_t *src_ip = NULL;
  const uint8_t *dst_ip = NULL;
  uint8_t        proto = 0;
  uint16_t       sport = 0, dport = 0, payload_len = 0;
  uint16_t       l4_offset = 0;
  uint16_t       l4_packet_len;

  const uint8_t *data_start = (uint8_t *)ip4_or_ip6;

  uint8_t version = *((const uint8_t *)ip4_or_ip6) >> 4;
  if (version == 4) {
    iph = (struct slc_iphdr *)ip4_or_ip6;
    src_ip = iph->saddr;
    dst_ip = iph->daddr;
    proto = iph->protocol;
    l4_offset += iph->ihl * 4;
    l4_packet_len = ntohs(iph->tot_len) - (iph->ihl * 4);
    if ((iph->ihl * 4) > ipsize || ipsize < ntohs(iph->tot_len)) {
      return PKT_DROP;
    }

  } else if (version == 6) {
    iph6 = (slc_ipv6hdr *)ip4_or_ip6;
    src_ip = iph6->ip6_src;
    dst_ip = iph6->ip6_dst;
    proto = iph6->ip6_ctlun.ip6_un1.ip6_un1_nxt;
    l4_offset += 40;
    l4_packet_len = ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen);
    if (ipsize < sizeof(const struct slc_ipv6hdr) || ipsize < 40 + ntohs(iph6->ip6_ctlun.ip6_un1.ip6_un1_plen)) {
      return PKT_DROP;
    }
  } else {
    return PKT_DROP;
  }

  l4 = data_start + l4_offset;

  if (proto == IPPROTO_TCP && l4_packet_len >= 20) {
    tcph = (const struct slc_tcphdr *)l4;
    sport = ntohs(tcph->source);
    dport = ntohs(tcph->dest);
    int tcp_len = SLC_MIN(4 * tcph->doff, l4_packet_len);
    payload = &l4[tcp_len];
    payload_len = l4_packet_len > (4 * tcph->doff) ? l4_packet_len - (4 * tcph->doff) : 0;
  } else if (proto == IPPROTO_UDP && l4_packet_len >= 8) {
    udph = (const struct slc_udphdr *)l4;
    sport = ntohs(udph->source);
    dport = ntohs(udph->dest);
    payload = &l4[sizeof(struct slc_udphdr)];
    payload_len = l4_packet_len > sizeof(struct slc_udphdr) ? l4_packet_len - sizeof(struct slc_udphdr) : 0;
  } else if (proto != IPPROTO_TCP && proto != IPPROTO_UDP) {
    return PKT_DROP;
  }

  five_tuple tuple;
  memset(&tuple, 0, sizeof(tuple));
  tuple.proto = proto;
  tuple.ip_version = version;
  memcpy(tuple.ip_src, src_ip, (version - 4) * 7 + 4);
  memcpy(tuple.ip_dst, dst_ip, (version - 4) * 7 + 4);
  tuple.port_src = htons(sport);
  tuple.port_dst = htons(dport);
  uint8_t C2S = sport > dport;

  slc_flow_info_t *flow_info = NULL;
  if (user) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(user);
    flow_info = decoder->findCreateFlow(&tuple);
  } else {
    flow_info = SLCPARSER->findCreateFlow(&tuple);
  }

  flow_info->user = user;

  if (tcph) {
    flow_info->tcph = tcph;
  }

  if (flow_info->packet_stage > 10 && flow_info->real_protocol_id == IPC_PROTOCOL_UNKNOWN) {
    return PKT_OK;
  }

  if (flow_info->real_protocol_id == IPC_PROTOCOL_UNKNOWN) {
    onIdentifyProto(flow_info, C2S, payload, payload_len);
    if (flow_info->cache_num < MAX_CACHE && tcph && flow_info->real_protocol_id == IPC_PROTOCOL_UNKNOWN) {
      uint8_t flg = 0;
      flg |= tcph->fin << 0;
      flg |= tcph->syn << 1;
      flg |= tcph->rst << 2;
      flg |= tcph->psh << 3;
      flow_info->cache[flow_info->cache_num].flag = flg;
      flow_info->cache[flow_info->cache_num].seq = ntohl(tcph->seq);
      flow_info->cache[flow_info->cache_num].ack_seq = ntohl(tcph->ack_seq);
      flow_info->cache[flow_info->cache_num].c2s = C2S;
      flow_info->cache_num++;
    }
  }

  if ((flow_info->real_protocol_id > IPC_PROTOCOL_UNKNOWN) && (flow_info->real_protocol_id < IPC_PROTOCOL_MAX)) {
    //如果有缓存报文 - 泄洪
    onDissectProto(flow_info, C2S, payload, payload_len);
  }

  return PKT_OK;
}

void SlcParser::onIdentifyProto(slc_flow_info_t *flow_info, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  notifyProtoKeeper(flow_info, C2S, payload, payload_len);
}

void SlcParser::onDissectProto(slc_flow_info_t *flow_info, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  notifyProtoKeeper(flow_info->real_protocol_id, flow_info, C2S, payload, payload_len);
}
