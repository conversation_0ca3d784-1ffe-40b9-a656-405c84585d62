#ifndef __SLC_REGISTER_H_
#define __SLC_REGISTER_H_

#include <memory>

#include "slc_flow.h"

class SlcProtoRegBase {
public:
  SlcProtoRegBase() {
    pNext_ = modules_;
    modules_ = this;
  }
  static void registerAllProtocol() {
    SlcProtoRegBase *p;
    for (p = modules_; p != NULL; p = p->pNext_) {
      p->registerProtocol();
    }
  }

  virtual ~SlcProtoRegBase() {}
  virtual void registerProtocol() = 0;

  virtual void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len, void *user) = 0;
  virtual void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len, void *user) = 0;
  virtual int  dissectProto_rsm(
       void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
  virtual int dissectProto_miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);

private:
  static SlcProtoRegBase *modules_;

private:
  SlcProtoRegBase *pNext_ = NULL;
};

template <typename ProtocolDecodeKeeper>
class SlcProtoLoader : public SlcProtoRegBase {
public:
  SlcProtoLoader(const SlcProtoLoader &) = delete;
  SlcProtoLoader(SlcProtoLoader &&) = delete;
  SlcProtoLoader &operator=(const SlcProtoLoader &) = delete;
  SlcProtoLoader &operator=(SlcProtoLoader &&) = delete;

  static std::shared_ptr<ProtocolDecodeKeeper> &GetInstance() { return myInstance; }

public:
  virtual void registerProtocol() override { keeper_ = ProtocolDecodeKeeper::GetInstance().get(); }

public:
  SlcProtoLoader() {}
  ~SlcProtoLoader() {}

private:
  static std::shared_ptr<ProtocolDecodeKeeper> myInstance;

  ProtocolDecodeKeeper *keeper_;
};

#endif