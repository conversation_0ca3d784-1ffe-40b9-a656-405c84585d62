
#ifndef __SLC_CAPTURE_H
#define __SLC_CAPTURE_H

#include <string>

#include "RtspServer.h"
#include "ipc/ipc.h"
#include "slc_config.h"
struct pcappkt_hdr {
    unsigned int tv_sec;      /* timestamp seconds */
    unsigned int tv_usec;     /* timestamp microseconds */
    unsigned int caplen;      /* number of octets of packet saved in file */
    unsigned int len;          /* actual length of packet */
};


enum YASLC_STATUS {
  YASLC_DISSECT,
  YASLC_NO,
  YASLC_YES,
};


typedef struct yaslc_status_t {
  IPC_PROTOCOL_TYPE type;
  YASLC_STATUS      status;
  std::string       rtsp_addr;
} yaslc_status_t;
int update_yaSlc_status(yaslc_status_t update_status) ;
class SlcCap {
public:
  SlcCap(const std::string &lRecordCntPerFile);
  ~SlcCap();

public:
  int inspectPcapFile(const char *pPcapFileName);

  int  inspectLiveCap(const char *pStrIf);
  void setRtspSession(xop::RtspServer *rtsp_server, xop::MediaSessionId session_id) {
    rtsp_server_ = rtsp_server;
    session_id_ = session_id;
  }

  std::string GenerateCapFileName();

  int parseSlcCapture();
  void clearCapInfo() ;
  void setCapFileSwitch(int cap_switch);
  void setCapFileName(std::string filename) { 
    // capFileName.clear();
    // capFileNameFinish.clear();
    capFileNameFinish = filename;
    capFileName = filename + ".writing";
   }
  void saveFile(const uint8_t *pPktData, int len);
  int  saveFileStart();
  void saveFileStop();

  FILE               *f = NULL;
  xop::RtspServer    *rtsp_server_;
  xop::MediaSessionId session_id_;
  IPC_DECODER        *decoder_handle;

private:
  static void onGotLivePkt(uint8_t *user, const struct pcap_pkthdr *h, const u_char *bytes);

  void parsePcapPkt(const timeval *pCapTs, int len, int pktNO, const uint8_t *pData);

private:
  std::string strSlcCapFilter_;

  //捕获文件参数
  bool        cap_switch_;
  FILE       *capFileFp = NULL;
  long        capFileSize_ = 0;
  long        capMaxFileSize_ = 0;
  std::string brand_;
  std::string model_;
  std::string orb_;
  std::string capFileName;
  std::string capFileNameFinish;
  std::string filepath_;
};

#endif
