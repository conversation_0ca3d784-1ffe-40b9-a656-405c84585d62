#include "rtp_h264_packer.h"
#include <algorithm>
#include <arpa/inet.h>

struct NaluHeader {
  NaluHeader(uint8_t naluHeader) : data(naluHeader) {}

  NaluType_e getType() {
    return (NaluType_e)(data & 0x1f);  // 取低 5 位;
  }

  uint8_t getNRI() { return (data & 0x6f) >> 5; }

  uint8_t data;
};

struct FuHeader {
  FuHeader(uint8_t fuHeader) : data(fuHeader) {}

  enum FU_type {
    FU_ill,  // 错误 fu
    FU_start,
    FU_middle,
    FU_end,
  };

  FU_type getType() {
    // 最高 bit 置 1
    if ((data & 0x80) == 0x80) {
      return FU_start;
    }

    // 次高 bit 置 1
    if ((data & 0x40) == 0x40) {
      return FU_end;
    }

    // 最高 2 bit 置 0
    if ((data & 0xc0) == 0) {
      return FU_middle;
    }

    return FU_ill;
  }

  NaluType_e getNaluType() { return (NaluType_e)(data & 0x18); }

  uint8_t data;
};

RtpH264Unpacker::RtpH264Unpacker(onGotNalu_callback_t callback, void *userdata) : onGotNalu_func_(callback), userdata_(userdata) {
  naluBuff_.reserve(1400);
}

// | 1 byte   | 2 byte     | n byte |
// nalu-header nalu-size-01 nalu-01 nalu-size-02 nalu-02
int RtpH264Unpacker::processSTAP_A(uint8_t *rtpPayload, int len) {
  for (int offset = 1; offset < len;) {
    // get nalu size and skip it.
    uint16_t *pNaluSize = (uint16_t *)&rtpPayload[offset];
    uint16_t  naluSize = ntohs(*pNaluSize);
    offset += 2;

    // one nalu
    NaluHeader header(rtpPayload[offset]);
    onGotNalu_func_(header.getType(), &rtpPayload[offset], naluSize, userdata_);

    // forward to next (size + nalu)
    offset += naluSize;
  }

  return 0;
}

int RtpH264Unpacker::processFU_A(uint8_t *rtpPayload, int len) {
  FuHeader fuHeader(rtpPayload[1]);
  if (fuHeader.getType() == FuHeader::FU_start) {
    uint8_t fu_identifier = rtpPayload[0];
    uint8_t fu_header = rtpPayload[1];
    uint8_t nalu_header = (fu_identifier & 0xe0) | (fu_header & 0x1f);

    naluBuff_.clear();

    // 保存 nalu header
    naluBuff_.push_back(nalu_header);

    // 2字节后数据
    std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));
  } else if (fuHeader.getType() == FuHeader::FU_middle) {
    // 2字节后数据
    std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));
  } else if (fuHeader.getType() == FuHeader::FU_end) {
    // 2字节后数据
    std::copy(&rtpPayload[2], &rtpPayload[len], std::back_inserter(naluBuff_));

    // 递交 nalu
    NaluHeader header(naluBuff_.data()[0]);
    onGotNalu_func_(fuHeader.getNaluType(), naluBuff_.data(), naluBuff_.size(), userdata_);
    naluBuff_.clear();
  } else {
    // TODO: log error type;
  }

  return 0;
}

int RtpH264Unpacker::enqueueRtpPayload(uint8_t *rtpPayload, int len) {
  NaluHeader header(rtpPayload[0]);

  NaluType_e naluType = header.getType();

  if (naluType == NT_SEI     //  "NAL unit - Supplemental enhancement information (SEI)"
      || naluType == NT_SPS  //  "NAL unit - Sequence parameter set"
      || naluType == NT_PPS || naluType == NT_slice_IDR || naluType == NT_slice_nIDR) {
    onGotNalu_func_(naluType, rtpPayload, len, userdata_);
  } else if (naluType == NT_STAP_A) {  // STAP-A: Single-time aggregation
    processSTAP_A(rtpPayload, len);
  } else if (naluType == NT_FU_A) {  // FU-A: Fragment Unit A
    processFU_A(rtpPayload, len);
  } else {
    // TODO: log error rtp nalu type
  }

  return 0;
}

RtpH264Packer::RtpH264Packer(onGotRtp_callback_t callback, void *userdata, void *context)
    : onGotRtp_func_(callback), userdata_(userdata), RtpBuff_(1400) {
  fu_ident.f_bit = 0;
  fu_ident.nal_reference_idc = 1;
  fu_ident.type = 28;
  context_ = context;
}

/*负责切割nalu帧*/
int RtpH264Packer::enqueueNaluPayload(NALU_t *NaluFragment) {
  int processLen = 0;
  int NRI = (*(NaluFragment->buffer.data()) & 0xE0) >> 5;
  if (NaluFragment->len < MAX_NALU_SLICE_LEN) {
    /*单帧*/
    onPackSingleNaluRtp(NaluFragment->buffer.data(), NaluFragment->len, RtpH264Packer::emNALU_SINGLE, NRI);
    return 0;
  }
  //第一包 跳过第一字节
  processLen++;
  onPackSingleNaluRtp(NaluFragment->buffer.data() + processLen, MAX_NALU_SLICE_LEN, RtpH264Packer::emNALU_START, NRI);
  processLen += MAX_NALU_SLICE_LEN;
  for (; processLen < NaluFragment->len; processLen += MAX_NALU_SLICE_LEN) {
    if (NaluFragment->len - processLen <= MAX_NALU_SLICE_LEN) {
      //最后一包
      onPackSingleNaluRtp(
          NaluFragment->buffer.data() + processLen, NaluFragment->len - processLen, RtpH264Packer::emNALU_END, NRI);
      timestamp_ += 3600;
      break;
    }
    onPackSingleNaluRtp(NaluFragment->buffer.data() + processLen, MAX_NALU_SLICE_LEN, RtpH264Packer::emNALU_SLICE, NRI);
  }

  return 0;
}

/*负责拼接单帧nalu头
*SPS PPS 无需拼接
*FU_A   第一包nalu分片将头部1字节去除  分别在头部加上2字节fu- 头
*/
int RtpH264Packer::onPackSingleNaluRtp(uint8_t *naluPayload, int len, int startOrEnd, int NRI) {
  if (naluPayload == nullptr || len > MAX_NALU_SLICE_LEN) {
    return -1;
  }
  uint8_t buffer[1500] = {0};
  int     send_len = 0;
  fu_ident.nal_reference_idc = NRI;
  if (startOrEnd == RtpH264Packer::emNALU_SINGLE) {
    memcpy(buffer, naluPayload, len);
    send_len = len;
  } else {
    /*fu-a 拼接两字节*/
    if (startOrEnd == RtpH264Packer::emNALU_START) {
      fu_header.s_bit = 1;
      fu_header.e_bit = 0;

    } else if (startOrEnd == RtpH264Packer::emNALU_END) {
      fu_header.s_bit = 0;
      fu_header.e_bit = 1;
    } else if (startOrEnd == RtpH264Packer::emNALU_SLICE) {
      fu_header.s_bit = 0;
      fu_header.e_bit = 0;
    }
    fu_header.nal_unit_type = NT_FU_A;
    global_logger.Debug("fu_header.nal_unit_type = ", NT_FU_A);
    memcpy(buffer, &fu_ident, 1);
    memcpy(buffer + 1, &fu_header, 1);
    memcpy(buffer + 2, naluPayload, len);
    send_len = len + 2;
  }
  onGotRtp_func_(buffer, send_len, timestamp_, userdata_, context_);
  return 0;
}

NaluH264Pack::NaluH264Pack(onGotRtp_callback_t onGotRtp_func_, void *context) : rtp_packer(onGotRtp_func_, this, context) {
  header_.first_byte.version = 2;
  header_.first_byte.padding = 0;
  header_.first_byte.ext = 0;
  header_.first_byte.cc = 0;
  header_.type = 96;
  header_.seq = 0;
  header_.ssrc = 1234;
  header_.timestamp = 0;
  context_ = context;
}