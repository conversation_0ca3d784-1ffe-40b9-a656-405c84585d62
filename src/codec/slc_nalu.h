#ifndef _SLC_NALU_H
#define _SLC_NALU_H

#include <map>
#include <mutex>
#include <vector>
#include <utility>
#include <queue>
#include <memory>

#include "slc_config.h"
#include "ipc/ipc.h"

typedef struct {
  uint8_t type : 5;
  uint8_t nal_reference_idc : 2;
  uint8_t f_bit : 1;
} __attribute__((__packed__)) FU_IDENTIFIER;  //! NALU FU identifier

typedef struct {
  uint8_t nal_unit_type : 5;
  uint8_t forbidden_bit : 1;
  uint8_t e_bit : 1;
  uint8_t s_bit : 1;
} __attribute__((__packed__)) FU_HEADER;  //! NALU FU Header

typedef struct {
  /***************本段为每帧nalu头的*******************/
  FU_IDENTIFIER fu_identifier;  //! NALU FU identifier
  FU_HEADER     fu_header;
  /**********************************/
  uint8_t naluType;
  uint8_t
      startcodeprefix_len;  //! NALU 中 header 长度 4 for parameter sets and first slice in picture, 3 for everything else (suggested)
  unsigned len;  //! NALU 中 EBSP 长度 Length of the NAL unit (Excluding the start code, which does not belong to the NALU)
  unsigned max_size;            //! 缓存数据时最大长度 Nal Unit Buffer size
  std::vector<uint8_t> buffer;  //! EBSP 缓存
} NALU_t;

#endif