#include "slc_capture.h"
#include "slc_config.h"
#include "common/tools.h"
#include <signal.h>
#include <cstring>
std::string rtxdr_cap_filter;
std::string plugin_path;
SlcCap* g_cap = nullptr;

void onSIGUSR1(int signum) {
  if (g_cap) {
    g_cap->setCapFileSwitch(1);
  }
  return ;
}
void onSIGUSR2(int signum) {
  if (g_cap) {
    g_cap->setCapFileSwitch(0);
  }
  return;
}

int main(int argc, char* argv[]) {
  // 解析命令行参数
  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "--plugin") == 0 && i + 1 < argc) {
      plugin_path = argv[i + 1];
      i++; // 跳过下一个参数
    }
  }

  // 创建抓包句柄
  g_cap = new SlcCap(rtxdr_cap_filter, plugin_path);

  yaslc_status_t status;
  status.type = IPC_PROTOCOL_UNKNOWN;
  status.status = YASLC_DISSECT;
  update_yaSlc_status(status);
  signal(SIGUSR1, onSIGUSR1);
  signal(SIGUSR2, onSIGUSR2);

  // go ...
  if (CFG->eMode_LiveCap == RUNMODE) {  // live cap

    printf("live capture mode on IF %s \n", INTERFACE_NAME);
    g_cap->inspectLiveCap(INTERFACE_NAME);

  } else if (CFG->eMode_OfflinePcap == RUNMODE) {  // offline pcap
    printf("offline mode on dir %s \n", PCAP_FILE_DIR);
    forDirEntry(PCAP_FILE_DIR, [](const char *pDirEntryName, bool bIsDir) {
      if (bIsDir) {
        return 0;
      }
      // while (1) {
      //   sleep(5);
      g_cap->inspectPcapFile(pDirEntryName);
      // }
      return 0;
    });
  }

  // 清理资源
  delete g_cap;
  g_cap = nullptr;

  return 0;
}