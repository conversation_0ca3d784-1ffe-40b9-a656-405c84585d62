#include "xop/RtspServer.h"
#include "net/Timer.h"
#include "slc_capture.h"
#include <thread>
#include "slc_config.h"
#include "common/tools.h"
#include <signal.h>
#include "slc_capture.h"
std::string rtxdr_cap_filter;
//构造抓包句柄
SlcCap cap(rtxdr_cap_filter);
void    onSIGUSR1(int signum) {
  cap.setCapFileSwitch(1);
  return ;
}
void onSIGUSR2(int signum) {
  cap.setCapFileSwitch(0);
  return;
}

int main() {
  yaslc_status_t status;
  status.type = IPC_PROTOCOL_UNKNOWN;
  status.status = YASLC_DISSECT;
  update_yaSlc_status(status);
  signal(SIGUSR1, onSIGUSR1);
  signal(SIGUSR2, onSIGUSR2);

  // 启动rtspServer
  std::string suffix = "live";
  std::string ip = "127.0.0.1";
  std::string port = "554";
  std::string rtsp_url = "rtsp://" + ip + ":" + port + "/" + suffix;

  std::shared_ptr<xop::EventLoop>  event_loop(new xop::EventLoop());
  std::shared_ptr<xop::RtspServer> server = xop::RtspServer::Create(event_loop.get());

  if (!server->Start("0.0.0.0", atoi(port.c_str()))) {
    printf("RTSP Server listen on %s failed.\n", port.c_str());
    return 0;
  }

#ifdef AUTH_CONFIG
  server->SetAuthConfig("-_-", "admin", "12345");
#endif

  xop::MediaSession *session = xop::MediaSession::CreateNew("live");
  session->AddSource(xop::channel_0, xop::H264Source::CreateNew());
  //session->StartMulticast();
  session->AddNotifyConnectedCallback([](xop::MediaSessionId sessionId, std::string peer_ip, uint16_t peer_port) {
    printf("RTSP client connect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
  });

  session->AddNotifyDisconnectedCallback([](xop::MediaSessionId sessionId, std::string peer_ip, uint16_t peer_port) {
    printf("RTSP client disconnect, ip=%s, port=%hu \n", peer_ip.c_str(), peer_port);
  });

  xop::MediaSessionId session_id = server->AddSession(session);
  cap.setRtspSession(server.get(), session_id);

  std::cout << "Play URL: " << rtsp_url << std::endl;

  // go ...
  if (CFG->eMode_LiveCap == RUNMODE) {  // live cap

    printf("live capture mode on IF %s \n", INTERFACE_NAME);
    cap.inspectLiveCap(INTERFACE_NAME);

  } else if (CFG->eMode_OfflinePcap == RUNMODE) {  // offline pcap
    printf("offline mode on dir %s \n", PCAP_FILE_DIR);
    forDirEntry(PCAP_FILE_DIR, [&cap](const char *pDirEntryName, bool bIsDir) {
      if (bIsDir) {
        return 0;
      }
      // while (1) {
      //   sleep(5);
      cap.inspectPcapFile(pDirEntryName);
      // }
      return 0;
    });
  }
  return 0;
}