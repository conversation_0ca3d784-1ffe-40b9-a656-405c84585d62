/*******************************************************************************
 * 文 件 名 : slc_hdlc.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - - - 设计: chenzq      2023/04/25
- - - - 编码: chenzq      2023/04/25
- - - - 修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -

*******************************************************************************/
#include "slc_hdlc.h"
#include <common/c_lang_linkage_start.h>

#include "slc_common.h"
#include <common/c_lang_linkage_end.h>

#include "slc_typedefs.h"

#define CISCO_SLARP     0x8035  /* Cisco SLARP protocol */

static const struct int_to_string chdlc_address_vals[] = {
  {CHDLC_ADDR_UNICAST,   "Unicast"},
  {CHDLC_ADDR_MULTICAST, "Multicast"},
  {0,                    NULL}
};

static const struct int_to_string chdlc_vals[] = {
  {0x2000,               "Cisco Discovery Protocol"},
  {ETHERTYPE_IP,         "IP"},
  {ETHERTYPE_IPv6,       "IPv6"},
  {CISCO_SLARP,          "SLARP"},
  {ETHERTYPE_DEC_LB,     "DEC LanBridge"},
  {CHDLCTYPE_BPDU,       "Spanning Tree BPDU"},
  {ETHERTYPE_ATALK,      "Appletalk"},
  {ETHERTYPE_AARP,       "AARP"},
  {ETHERTYPE_IPX,        "Netware IPX/SPX"},
  {ETHERTYPE_ETHBRIDGE,  "Transparent Ethernet bridging" },
  {CHDLCTYPE_OSI,        "OSI" },
  {ETHERTYPE_MPLS,       "MPLS unicast"},
  {ETHERTYPE_MPLS_MULTI, "MPLS multicast"},
  {0,                     NULL}
};


uint8_t slc_is_chdlc_header(const uint8_t *pkt, uint32_t pkt_len)
{
    if (pkt == NULL || pkt_len < 2) {
        return 0;
    }
    // check pkt[0] is in chdlc_address_vals
    if (pkt[0] != CHDLC_ADDR_UNICAST && pkt[0] != CHDLC_ADDR_MULTICAST) {
        return 0;
    }

    // check pkt[1] == 0x00
    if (pkt[1] != 0x00) {
        return 0;
    }

    // check pkt[2-3] in chdlc_vals
    uint16_t etype = (pkt[2] << 8) | pkt[3];
    if (val_to_string(etype, chdlc_vals) == NULL) {
        return 0;
    }

    return 1;
}