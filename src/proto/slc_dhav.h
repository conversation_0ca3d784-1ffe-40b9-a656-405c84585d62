
#ifndef _SLC_DHAV_H
#define _SLC_DHAV_H
#include <map>
#include <functional>
#include <netinet/in.h>

#include "slc_register.h"
#include "slc_observer.h"
#include "slc_singleton.h"
#include "slc_common.h"
#include "slc_sdp.h"

#define DHAV_PORT 37777

#define DHAV_NEED_MORE -1
#define DHAV_ERROR -2

class DHAVStream : flow_info {
public:
  DHAVStream(flow_info *flow_info) {
  user_ = flow_info->user;
  cache[0].clear();
  cache[1].clear();}
  int dissect_dhav_strip(void *user, int C2S, const uint8_t *payload, uint16_t pl);
  int dissect_dhav_miss(flow_info *flow, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);

private:
  int dissect_dhav(int C2S, const uint8_t *payload, const uint32_t payload_len);
  int find_dhav_start(uint8_t *p, int l);
  int get_dhav_len(uint8_t *p, int l);
  int is_dhav_start(const char *p, int l);

  int get_h264_len(const uint8_t *payload, const uint32_t payload_len);
  int find_h264_start(uint8_t *p, int l) ;

  int sendDHAVToDecoder(int C2S, const uint8_t *payload, const uint32_t payload_len);

  std::vector<uint8_t> cache[2];
  std::vector<uint8_t> h264_buff[2];

  void *user_ = NULL;  // decoder指针 决定了改stream归属
};
#define DHAVKEEPER DHAVKeeper::GetInstance()

class DHAVKeeper : public ObserverSessionKeeper, public singleton<DHAVKeeper> {
  friend class SlcProtoLoader<DHAVKeeper>;

public:
  DHAVKeeper();
  void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  int  dissectProto_rsm(void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg);
  int  miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len);

  std::map<flow_info *, std::shared_ptr<DHAVStream>> map_streams_;
};

#endif
