/*******************************************************************************
 * 文 件 名 : slc_hdlc.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - - - 设计: chenzq      2023/04/25
- - - - 编码: chenzq      2023/04/25
- - - - 修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
- - -

*******************************************************************************/
#ifndef __SLC_HDLC_H
#define __SLC_HDLC_H

#include <stdio.h>
#include <stdint.h>


#define CHDLC_ADDR_UNICAST	0x0f
#define CHDLC_ADDR_MULTICAST	0x8f

#define CHDLCTYPE_FRARP		0x0808	/* Frame Relay ARP */
#define CHDLCTYPE_BPDU		0x4242	/* IEEE spanning tree protocol */
#define CHDLCTYPE_OSI 	        0xfefe  /* ISO network-layer protocols */

struct slc_chdlc
{
    uint8_t addr;          /* 0x0F (Unicast) - 0x8F (Broadcast) */
    uint8_t ctrl;          /* always 0x00                       */
    uint16_t proto_code;   /* protocol type (e.g. 0x0800 IP)    */
};

uint8_t slc_is_chdlc_header(const uint8_t *pkt, uint32_t pkt_len);



#endif