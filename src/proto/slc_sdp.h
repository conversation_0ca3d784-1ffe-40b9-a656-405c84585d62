
#ifndef __SLC_SDP_H
#define __SLC_SDP_H

#include <string>
#include <list>
#include <vector>
#include "slc_common.h"

#include "common/c_lang_linkage_start.h"
#include "glib-2.0/glib.h"
/****************************************************************************************
 * 文 件 名 : dpi_sdp.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/18
编码: wangy            2018/07/18
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_SDP_H_
#define _DPI_SDP_H_

/*rtp head ，copy from wireshark ,
        now we do not have rtp dissector, but we need it ,so it is here*/

#define PT_PCMU 0        /* RFC 3551 */
#define PT_1016 1        /* RFC 1890 (reserved in RFC 3551) */
#define PT_G721 2        /* RFC 1890 (reserved in RFC 3551) */
#define PT_GSM 3         /* RFC 3551 */
#define PT_G723 4        /* From Vineet Kumar of Intel; see the Web page */
#define PT_DVI4_8000 5   /* RFC 3551 */
#define PT_DVI4_16000 6  /* RFC 3551 */
#define PT_LPC 7         /* RFC 3551 */
#define PT_PCMA 8        /* RFC 3551 */
#define PT_G722 9        /* RFC 3551 */
#define PT_L16_STEREO 10 /* RFC 3551 */
#define PT_L16_MONO 11   /* RFC 3551 */
#define PT_QCELP 12      /* Qualcomm Code Excited Linear Predictive coding? */
#define PT_CN 13         /* RFC 3389 */
#define PT_MPA 14        /* RFC 3551, RFC 2250 */
#define PT_G728 15       /* RFC 3551 */
#define PT_DVI4_11025 16 /* from Joseph Di Pol of Sun; see the Web page */
#define PT_DVI4_22050 17 /* from Joseph Di Pol of Sun; see the Web page */
#define PT_G729 18
#define PT_CN_OLD 19 /* Payload type reserved (old version Comfort Noise) */
#define PT_CELB 25   /* RFC 2029 */
#define PT_JPEG 26   /* RFC 2435 */
#define PT_NV 28     /* RFC 1890 */
#define PT_H261 31   /* RFC 2032 */
#define PT_MPV 32    /* RFC 2250 */
#define PT_MP2T 33   /* RFC 2250 */
#define PT_H263 34   /* from Chunrong Zhu of Intel; see the Web page */

/* Added to by Alex Lindberg to cover port ranges 96-127 - Dynamic RTP
   Some of these ports are used by Avaya for Modem and FAX support */

#define PT_UNDF_96 96 /* RFC 3551 */
#define PT_UNDF_97 97
#define PT_UNDF_98 98
#define PT_UNDF_99 99
#define PT_UNDF_100 100
#define PT_UNDF_101 101
#define PT_UNDF_102 102
#define PT_UNDF_103 103
#define PT_UNDF_104 104
#define PT_UNDF_105 105
#define PT_UNDF_106 106
#define PT_UNDF_107 107
#define PT_UNDF_108 108
#define PT_UNDF_109 109
#define PT_UNDF_110 110
#define PT_UNDF_111 111
#define PT_UNDF_112 112
#define PT_UNDF_113 113
#define PT_UNDF_114 114
#define PT_UNDF_115 115
#define PT_UNDF_116 116
#define PT_UNDF_117 117
#define PT_UNDF_118 118
#define PT_UNDF_119 119
#define PT_UNDF_120 120
#define PT_UNDF_121 121
#define PT_UNDF_122 122
#define PT_UNDF_123 123
#define PT_UNDF_124 124
#define PT_UNDF_125 125
#define PT_UNDF_126 126
#define PT_UNDF_127 127

typedef struct _value_string {
  uint32_t    value;
  const char *strptr;
} value_string;

#define SDP_O_USERNAME "o_username"
#define SDP_O_SESSIONID "o_sessionid"
#define SDP_O_VERSION "o_version"
#define SDP_O_NETWORK_TYPE "o_network_type"
#define SDP_O_ADDRESS_TYPE "o_address_type"
#define SDP_O_ADDRESS "o_address"
#define SDP_C_NETWORK_TYPE "c_network_type"
#define SDP_C_ADDRESS_TYPE "c_address_type"
#define SDP_C_ADDRESS "c_address"
#define SDP_T_TIME_START "t_time_start"
#define SDP_T_TIME_END "t_time_end"
#define SDP_M_TYPE "m_type"
#define SDP_M_PORT "m_port"
#define SDP_M_PROTO "m_proto"
#define SDP_M_PAYLOADS "m_payloads"
#define SDP_V_VERSION "v_version"
#define SDP_S_NAME "s_name"
#define SDP_I_INFO "i_info"
#define SDP_U_URI "u_uri"
#define SDP_E_EMAIL "e_email"
#define SDP_P_PHONE "p_phone"
#define SDP_B_BANDWIDTHS "b_bandwidths"
#define SDP_R_REPEATTIME "r_repeattime"
#define SDP_A_ATTRIBUTES "a_attributes"

#define MEDIA_PAYLOAD_LEN_MAX 128

#define SDP_MEDIA_MAX_NUM 6
#define SDP_M_ATTR_MAX_NUM 40
#define SIP_REPEAT_GROUP_NUM 10

typedef struct __body_value {
  uint8_t        need_free;
  uint16_t       len;
  const uint8_t *ptr;
} body_value;

typedef body_value content_value;

typedef struct __sip_via_header {
  char transport[64];
  char sent_addr[64];
  char sent_port[64];
  char branch[64];
  char rport[64];
  char recieved[64];
} sip_via_header;

typedef struct __stand_address {
  char user_part[64];
  char e164_num[64];
  char country_code[64];
  char host_part[64];
  char host_port[64];
} stand_address;

typedef struct __sip_from_header {
  stand_address addr;
  char          from_tag[64];
} sip_from_header;

typedef struct __sip_to_header {
  stand_address addr;
  char          to_tag[64];
} sip_to_header;

typedef struct __sip_cseq_header {
  char sequence_num[64];
  char method[64];
} sip_cseq_header;

typedef struct __sip_contact_header {
  stand_address addr;
  char          param[64];
} sip_contact_header;

typedef struct __owner_session {
  char name[64];
  char session_id[64];
  char session_version[64];
  char network_type[64];
  char adddress_type[64];
  char address[64];
} owner_session;

typedef struct __connection {
  char networdk_type[64];
  char address_type[64];
  char address[64];
} connection;

typedef struct __time_descrip {
  char start_time[64];
  char stop_time[64];
  char keep_time[64];
} time_descrip;

typedef struct __media_descrip {
  char media_type[64];
  char media_port[64];
  char media_protocol[64];
  char media_format1[64];
  char media_format2[64];
} media_descrip;

typedef struct __media_attribute {
  char attr_name[64];
  char format[64];
  char mime_type[64];
  char sample_rate[64];
  char specific_param[64];
  char attr_value[64];
} media_attribute;

typedef struct __sip_body_gather {
  owner_session   owner;
  connection      connec;
  time_descrip    time;
  media_descrip   m_descrip;
  media_attribute m_attr[SIP_REPEAT_GROUP_NUM];
  int             attr_num;
} sip_body_gather;
typedef struct __sip_header_gather {
  char           via_line[64] = {0};
  sip_via_header via;

  char            from_line[64] = {0};
  sip_from_header from;

  char          to_line[64] = {0};
  sip_to_header to;

  char            cseq_line[64] = {0};
  sip_cseq_header cseq;

  char               contact_line[64] = {0};
  sip_contact_header contact;

  char user_agent[64] = {0};
} sip_header_gather;

struct sip_request_info {
  uint16_t header_num;
  uint16_t empty_line_position;
  PROTOCOL_HEAD_DEF(head_line)
  PROTOCOL_HEAD_DEF(method)
  PROTOCOL_HEAD_DEF(version)
  PROTOCOL_HEAD_DEF(status_code)
  PROTOCOL_HEAD_DEF(uri)
  PROTOCOL_HEAD_DEF(message_header)
  PROTOCOL_HEAD_DEF(message_body)

  uint64_t          sip_rtp_flowid;
  uint8_t           voip_data[256];
  char              smsText[512];
  GHashTable       *table;
  sip_header_gather header_gather;
  sip_body_gather   body_gather;
};
struct sdp_m_info {
  char m_media[256];

  char m_type[64];
  char m_port[64];
  char m_proto[64];
  char m_title[64];
  char m_payloads[MEDIA_PAYLOAD_LEN_MAX];
  int  a_attr_num;
  char a_attributes[SDP_M_ATTR_MAX_NUM][256];
};

struct sdp_info {
  char o_owner[256];
  char o_username[64];
  char o_sessionid[64];
  char o_version[64];
  char o_network_type[64];
  char o_address_type[64];
  char o_address[64];
  char c_info[256];
  char c_network_type[64];
  char c_address_type[64];
  char c_address[64];
  char t_time[128];
  char t_time_start[64];
  char t_time_end[64];
  char v_version[64];
  char s_name[64];
  char i_info[64];
  char u_uri[64];
  char e_email[64];
  char p_phone[64];
  char b_bandwidths[64];
  char r_repeattime[64];

  char session_attribute[SDP_M_ATTR_MAX_NUM];

  struct sdp_m_info m_info[SDP_MEDIA_MAX_NUM];
  int               m_info_num;
};

#endif

#include "common/c_lang_linkage_end.h"
struct sdp_connection {
  std::string c_nettype;
  std::string c_addrtype;
  std::string c_addr;
  std::string c_addr_multicast_tll;
  std::string c_addr_muticast_int;
};
class sdp_message {
  std::string            v_version;
  std::string            o_username;
  std::string            o_sess_id;
  std::string            o_sess_version;
  std::string            o_nettype;
  std::string            o_addrtype;
  std::string            o_addr;
  std::string            s_name;
  std::string            i_info;
  std::string            u_uri;
  std::string            z_adjustments;
  std::list<std::string> e_emails;
  std::list<std::string> p_phones;
  std::list<std::string> b_bandwiths;
  std::list<std::string> t_descrs;
  std::list<std::string> m_medias;
  sdp_connection         c_connection;
};

class SdpInfo {
public:
  SdpInfo() { memset(&info, 0, sizeof info); }
  // 处理sdp中有效的信息
  void            dissectSipMeidaDescription(struct flow_info *flow);
  void            dissectSipSDP(struct flow_info *flow);
  int             dissect_sdp(struct flow_info *flow, int direction, const uint8_t *payload, const uint32_t payload_len);
  void            setSipinfo(struct flow_info *flow, struct sip_request_info &sip_info);
  void            SetRtspinfo(struct flow_info *flow, char *cli_port, char *ser_port);
  void            setuser(void *user) { user_ = user; }
  struct sdp_info info;

private:
  // 将sdp解析到info中(dpi 移植代码)
  void _copy_one_item(char *result, int max_len, const uint8_t *value, uint16_t len);
  void _add_one_item_to_hash(GHashTable *table, const char *header, const uint8_t *value, uint16_t len);
  void dissect_sdp_owner(const uint8_t *line, int line_len, GHashTable *table);
  void dissect_sdp_connnection_info(const uint8_t *line, int line_len, GHashTable *table);
  void dissect_sdp_time(const uint8_t *line, int line_len, GHashTable *table);
  void find_media_payload(const uint8_t *start, int len, char *result, uint16_t *len_ptr);
  void dissect_sdp_media(const uint8_t *line, int line_len, GHashTable *table);
  void dissect_sdp_time2(const uint8_t *line, int line_len);
  void dissect_sdp_connnection_info2(const uint8_t *line, int line_len);
  void dissect_sdp_owner2(const uint8_t *line, int line_len);
  void find_media_payload2(const uint8_t *start, int len, char *result, int max_len);
  void dissect_sdp_media2(const uint8_t *line, int line_len);

public:
  uint32_t rtp_src_port = 0;
  uint32_t rtp_dst_port = 0;

  std::vector<std::string> sipMeidaDescription_v;
  std::string              server_ip_;
  std::string              cli_ip_;
  std::string              sip_status_;
  std::string              sip_from_;
  std::string              sip_to_;
  std::string              user_agent;
  void                    *user_;  //decoder指针
  int                      conversion_flag_ = 0;
  int                      sample_rate_;
  uint32_t                 ssrc;
};
#endif