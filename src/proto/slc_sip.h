

#ifndef _SLC_SIP_H
#define _SLC_SIP_H
#include <map>
#include <functional>
#include "glib-2.0/glib.h"

#include "slc_sdp.h"
#include "slc_register.h"
#include "slc_observer.h"
#include "slc_singleton.h"
#include "slc_common.h"

#define FIND_FUNC 1
#define NOT_FIND_FUNC 0

class SipStream : public flow_info {
  uint16_t stream_status = 0;
  uint8_t  voip_status = 0;

  struct five_tuple rtp_tuple;  //正向五元组
  /* from sip */
  std::string payload_name;
  std::string sample_rate;
  std::string from;
  std::string to;

public:
  uint32_t ssrc = 0;
  SipStream(flow_info *f_info) : flow_info(*f_info) { stream_status = 0; }
  SdpInfo sdp_;
};

#define SIPKEEPER SIPKeeper::GetInstance()

class SIPKeeper : public ObserverSessionKeeper, public singleton<SIPKeeper> {
  friend class SlcProtoLoader<SIPKeeper>;

public:
  SIPKeeper();
  void identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);
  void dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len);

private:
  std::map<flow_info *, std::shared_ptr<SipStream>> map_streams_;
};

#endif