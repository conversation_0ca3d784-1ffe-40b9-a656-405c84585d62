

#include "ipc/ipc_decoder.h"
#include "slc_common.h"
#include "slc_dhav.h"
#include "slc_tcp.h"
#include <string.h>
#include <time.h>
#include <unistd.h>
int DHAVStream::find_h264_start(uint8_t *p, int l) {
  int offset = DHAV_NEED_MORE;
  for (int i = 0; i < l - 4; i++) {
    if (p[i] == 0x00 && p[i + 1] == 0x00 && p[i + 2] == 0x00 && p[i + 3] == 0x01) {
      offset = i;
      return offset;
    }
  }
  return DHAV_NEED_MORE;
};

int DHAVStream::get_h264_len(const uint8_t *payload, const uint32_t payload_len) {
  //payload 开头一定为 00000001
  int len = DHAV_NEED_MORE;
  len = find_h264_start((uint8_t *)payload + 4, payload_len - 4);
  if (len >= 0) {
    len += 4;
  }
  return len;
}

int DHAVStream::sendDHAVToDecoder(int C2S, const uint8_t *payload, const uint32_t payload_len) {
  uint8_t     *p = (uint8_t *)payload;
  int          pl = payload_len;
  int          fl = 0;
  int          offset = 0;
  int          start = 0;
  auto         c = h264_buff + C2S;
  ipc_decoder *decoder = static_cast<ipc_decoder *>(user_);

  if (c->empty()) {
    //保证第一次进来为 264 nalu单元开头
    if (payload[0] != 0 && payload[1] != 0 && payload[2] != 0 && payload[3] != 0x01) {
      const char *find = (const char *)memmem(payload, 4, "\x00\x00\x00\x01", 4);
      if (NULL == find) {
        return 0;
      }
      p = (uint8_t *)find;
      pl = pl - (p - payload);
    }
  }
  std::copy(&p[0], &p[pl], std::back_inserter(*c));
  pl = c->size();
  p = c->data();

  while (offset < pl) {
    start = find_h264_start((uint8_t *)p + offset, pl - offset);
    if (DHAV_NEED_MORE == start) {
      // 没找到开头 继续缓存
      break;
    }
    offset += start;
    fl = get_h264_len(p + offset, pl - offset);
    if (fl < 0) {
      break;
    }
    decoder->onNaluCallback(NT_none, (uint8_t *)p + offset + 4, fl - 4, this);
    offset += fl;
  }
  if (offset >= 0 && offset < pl) {
    c->erase(c->begin(), c->begin() + offset);
  } else {
    if (TRUE != c->empty()) {
      c->clear();
    }
  }

  return 0;
}

/**
 * @brief 处理重组后的 32 字节头为开始的 dhav 帧
 * 
 * @param flow 
 * @param payload 
 * @param payload_len 
 * @return int 
 */
int DHAVStream::dissect_dhav(int C2S, const uint8_t *payload, const uint32_t payload_len) {
  int len = 0;
  int offset = 0;
  //32字节头
  offset += 32;

  // 大华头
  // 头部4字节大华标识，0x44484156，即“DHAV”
  // 第一次进入的时候 保证为 DHAV 开始
  if (payload[offset] == 0x44 && payload[offset + 1] == 0x48 && payload[offset + 2] == 0x41 && payload[offset + 3] == 0x56) {
    offset += 4;
    int frame_len = get_uint32_t(payload, offset + 8);
    if (payload[offset] == 0xFC || payload[offset] == 0xFD) {  // H264编码的P帧
      offset += 36;
    } else {
      return 0;
    }
  }

  // 末尾8字节
  // 尾部4字节大华标识，0x64686176，即“dhav”
  // +4字节，长度指示，与前面32/40字节中的长度指示一致）
  if (payload[payload_len - 8] == 0x64 && payload[payload_len - 7] == 0x68 && payload[payload_len - 6] == 0x61 &&
      payload[payload_len - 5] == 0x76) {
    len = payload_len - offset - 8;
  } else {
    len = payload_len - offset;
  }

  if (len > 0) {
    sendDHAVToDecoder(C2S, payload + offset, len);
  }
  return 0;
}

int DHAVStream::find_dhav_start(uint8_t *p, int l) {
  int offset = DHAV_NEED_MORE;
  for (int i = 0; i < l - 3; i++) {
    if (p[i] == 0xbc && p[i + 1] == 0x00 && p[i + 2] == 0x00 && p[i + 3] == 0x00) {
      offset = i;
      return offset;
      break;
    }
  }
  return DHAV_NEED_MORE;
}

int DHAVStream::is_dhav_start(const char *p, int l) {
  if (l < 32)
    return DHAV_NEED_MORE;
  // 4字节固定，0xBC000000
  const char *find = (const char *)memmem(p, 4, "\xBC\x00\x00\x00", 4);
  if (NULL == find) {
    return DHAV_NEED_MORE;
  }
  // 4字节长度指示，高位在右，指示“大华info”的字节数
  // 6字节固定，0x020000000000
  const char *find_1 = (const char *)memmem(find + 8, 6, "\x00\x00\x00\x00\x00\x00", 6);
  if (NULL == find_1) {
    return DHAV_NEED_MORE;
  }
  return 1;
}

/**
 * @brief Get the dhav len object
 * 此函数保证 p 为dhav 32字节头
 * @param p 
 * @param l 
 * @return int 为正数:长度 小于 0：DHAV_NEED_MORE
 */
int DHAVStream::get_dhav_len(uint8_t *p, int l) {
  int len = 0;
  if (l < 8) {
    return DHAV_NEED_MORE;
  }
  len = get_uint16_t(p, 4) + 32;  //4字节指示 `DHAV....` 的长度 + 32 字节头
  //下一帧起始位置
  if (l < len + 4) {
    return DHAV_NEED_MORE;
  }
  int start = find_dhav_start(p + len, l - len);
  if (start == 0) {
    //长度有效
    return len;
  } else {
    return DHAV_ERROR;
  }
}
/**
 * @brief dhav切割机
 *此函数需要保证每切割出来的一帧为完整的dhav帧
 * @param user 
 * @param C2S 
 * @param p 
 * @param pl 
 * @return int 
 */

int DHAVStream::dissect_dhav_strip(void *user, int C2S, const uint8_t *payload, uint16_t pl) {
  char     t = 0;
  int64_t  hl = 0;  // dhav单帧长度
  int64_t  offset = 0;
  uint8_t *p = (uint8_t *)payload;
  int64_t  l = pl;  // 单帧tcp_payload长度
  int      start = DHAV_NEED_MORE;

  auto c = cache + C2S;

  // 是否开启缓存
  if (!c->empty()) {
    // 无论如何，先进行拼装操作
    //正常 拼装
    std::copy(&payload[0], &payload[pl], std::back_inserter(*c));
    p = c->data();
    l = c->size();
  }

  // 缓存为空   p 为 payload  ; l 为 payload_len
  // 缓存不为空 p 为 c->cache ; l 为 c->cache_hold

  // 专业切割机
  // 切割规则: 以32字节头0xBC000000作为分割标识 每次将
  // 0xBC000000 + 28 + payload 送入dissect_dhav
  while (offset + 4 < l) {
    start = find_dhav_start(p + offset, l - offset);
    if (DHAV_NEED_MORE == start) {
      // 没找到开头 继续缓存
      goto DHAV_NEED_MORE_PKT;
    }
    offset += start;
    hl = get_dhav_len(p + offset, l - offset);
    if (hl > 0) {
      dissect_dhav(C2S, (const uint8_t *)p + offset, (uint32_t)hl);
      offset += hl;
    } else if (hl == DHAV_ERROR) {
      offset += 4;  //找到`BC00` 但是长度不对 证明当前的BC00 是payload中的内容 跳过当前`BC 00`
    } else if (hl == DHAV_NEED_MORE) {
      break;
    }
  }

  // 有没有剩料?
  if (offset >= 0 && offset < l) {
    if (TRUE != c->empty() && offset > 0)  //已开启缓存, 直接将剩料挪到前面
    {
      c->erase(c->begin(), c->begin() + offset);
    } else if (TRUE == c->empty())  //未开启缓存, 创建缓存, 把剩料放在前面
    {
      //正常 拼装
      std::copy(p + offset, p + offset + (l - offset), std::back_inserter(*c));
    }
  } else {
    if (TRUE != c->empty()) {
      c->clear();
    }
  }
  goto DHAV_NEED_MORE_PKT;

DHAV_DROP:
  // 在切割时 不期望丢失任何数据 理论上这里不会运行到
  if (TRUE != c->empty()) {
    c->clear();
  }
// DHAV 解析,需要更多报文
DHAV_NEED_MORE_PKT:
  return 0;
}

int DHAVStream::dissect_dhav_miss(flow_info *flow, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len) {
  auto c = cache + C2S;
  c->insert(c->end(), miss_len, 0);
  global_logger.Debug("dissect_dhav_miss miss len: %d",miss_len);

  return 0;
}

auto dhavkeeper = DHAVKEEPER;

DHAVKeeper::DHAVKeeper() {
  setRegCallback(IPC_PROTOCOL_DHAV,"dhav",
      std::bind(&DHAVKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&DHAVKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&DHAVKeeper::dissectProto_rsm, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4, std::placeholders::_5, std::placeholders::_6, std::placeholders::_7),
      std::bind(
          &DHAVKeeper::miss, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
}
void DHAVKeeper::dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  return;  //没重组的情况下不符合格式，暂时不写
}
int DHAVKeeper ::dissectProto_rsm(
    void *user, uint8_t C2S, const uint8_t *ptr, uint32_t len, uint32_t seq, uint32_t ack, uint32_t flg) {
  flow_info                  *flow = static_cast<flow_info *>(user);
  std::shared_ptr<DHAVStream> stream = nullptr;

  auto it = map_streams_.find(flow);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    auto newStreamPtr = std::make_shared<DHAVStream>(flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<DHAVStream>()");
      return -1;
    }
    stream = newStreamPtr;
    map_streams_[flow] = newStreamPtr;
  }
  stream->dissect_dhav_strip(flow, C2S, ptr, len);
  return 0;
}

int DHAVKeeper::miss(void *user, uint8_t C2S, uint32_t miss_seq, uint32_t miss_len) {
  flow_info                  *flow = static_cast<flow_info *>(user);
  std::shared_ptr<DHAVStream> stream = nullptr;

  auto it = map_streams_.find(flow);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    auto newStreamPtr = std::make_shared<DHAVStream>(flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<DHAVStream>()");
      return -1;
    }
    stream = newStreamPtr;
    map_streams_[flow] = newStreamPtr;
  }
  stream->dissect_dhav_miss(flow, C2S, miss_seq, miss_len);
  return 0;
}

void DHAVKeeper::identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  if (payload_len < 32)
    return;

  uint16_t s_port = 0, d_port = 0;
  s_port = ntohs(flow->tuple.port_src);
  d_port = ntohs(flow->tuple.port_dst);

  if (s_port != 37777 && d_port != 37777) {
    return;
  }

  flow->real_protocol_id = IPC_PROTOCOL_DHAV;
}