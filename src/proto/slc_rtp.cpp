#include "slc_rtp.h"
#include "ipc/ipc_decoder.h"
#include "slc_config.h"
#include "slc_logger.h"
#include "slc_detect.h"
#include "slc_rtsp.h"

extern slc::Logger global_logger;

auto rtpkeeper = RTPKEEPER;

int NaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  RtpStream *stream = (RtpStream *)(userdata);
  if (stream->hasUserData()) {
    ipc_decoder *decoder = static_cast<ipc_decoder *>(stream->getUserData());

    decoder->onNaluCallback(naluType, nalu, naluLen, (void *)stream);
  } else {
    stream->onNaluCallback(naluType, nalu, naluLen, userdata);
  }

  return 0;
}

RtpStream::RtpStream(RtpFlowKey &flowKey, flow_info *f_info) : Unpacker_(NaluCallback, this), flow_info(*f_info) {
  user_ = f_info->user;
  payload_type = flowKey.payload_type;
  ssrc = flowKey.ssrc;
  ipc_decoder *decoder = static_cast<ipc_decoder *>(user_);
  if (MediaType == RtpMediaType::unknown) {
    MediaType = RTPKEEPER->getRtpMediaType(*this);
  }
  //通过rtsp建立的RTP
  if (f_info->real_protocol_id == IPC_PROTOCOL_RTSP || f_info->create_by == IPC_PROTOCOL_RTSP) {
    std::shared_ptr<RtspStream> stream = RTSPKEEPER->findRtspStreamByFlow(f_info);
    if (stream != NULL) {
      decoder->creatIpcStreamFromRTSP(stream.get(), this, ssrc, this->MediaType, (uint8_t *)stream->sps_, stream->sps_len_,
          (uint8_t *)stream->pps_, stream->pps_len_);
    }
  }
}

void RtpStream::enqueRtpStreamFragment(uint8_t *payload, uint32_t payload_len) {
  if (this->tuple.proto == 6) {
    if (payload[payload_len - 1] == 0x01) {
      payload_len--;
    } else if (payload[payload_len - 1] == 0x02) {
      payload_len -= 2;
    } else if (payload[payload_len - 1] == 0x03) {
      payload_len -= 3;
    }
  }
  if(MediaType == RtpMediaType::unknown && create_by == IPC_PROTOCOL_RTSP){
    MediaType = create_by_mediaType;
  }
  if (MediaType == RtpMediaType::video_h264)
    Unpacker_.enqueueRtpPayload(payload, payload_len);
}

int RtpStream::onNaluCallback(NaluType_e naluType, uint8_t *nalu, int naluLen, void *userdata) {
  NALU_t *n = new NALU_t;
  if (n == nullptr) {
    global_logger.Debug("new NALU_t bad new");
    return 0;
  }

  n->naluType = naluType;
  n->buffer.resize(naluLen);
  n->len = naluLen;
  std::copy(nalu, nalu + naluLen, n->buffer.begin());
  frameSeq_++;
  return 0;
}

RtpKeeper::RtpKeeper() {
  setRegCallback(IPC_PROTOCOL_RTP, "rtp",
      std::bind(&RtpKeeper::identifyProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      std::bind(&RtpKeeper::dissectProto, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
          std::placeholders::_4),
      NULL, NULL);
}

void RtpKeeper ::identifyProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  if (payload_len < 12) {
    return;
  }
  uint16_t d_port = 0;
  d_port = ntohs(flow->tuple.port_dst);
  if (d_port <= 1023) {
    return;
  }

  unsigned int    version;
  static uint16_t fs_last_seq = 0;
  version = CACL_RTP_VERSION(get_uint8_t(payload, 0));
  if (!(version == 2)) {
    return;
  }

  uint8_t  payload_type = payload[1] & 0x7F;
  uint16_t rtp_seq = get_uint16_ntohs(payload, 2);

  if (payload_type >= 96) {
    //if (((payload_type < 72) || (payload_type > 76)) && ((payload_type <= 34) || ((payload_type > 96) && (payload_type <= 127)))) {
    if (flow->packet_stage == 0) {
      flow->userdata[0] = payload_type;
      // 比较ssrc是否一致
      flow->userdata[1] = get_uint8_t(payload, 8);
      flow->userdata[2] = get_uint8_t(payload, 9);
      flow->userdata[3] = get_uint8_t(payload, 10);
      flow->userdata[4] = get_uint8_t(payload, 11);
    } else if ((flow->userdata[1] != get_uint8_t(payload, 8) && flow->userdata[2] != get_uint8_t(payload, 9) &&
                   flow->userdata[3] != get_uint8_t(payload, 10) && flow->userdata[4] != get_uint8_t(payload, 11) &&
                   flow->userdata[0] != payload_type) ||
               (((flow->userdata[5] + 1) != get_uint8_t(payload, 2)) && ((flow->userdata[6] + 1) != get_uint8_t(payload, 3)))) {
      flow->packet_stage = 0;
      return;
    }
    flow->userdata[5] = get_uint8_t(payload, 2);
    flow->userdata[6] = get_uint8_t(payload, 3);
    if (flow->packet_stage == 3) {
      global_logger.Info("identifyProto = RTP");
      flow->real_protocol_id = IPC_PROTOCOL_RTP;
      flow->packet_stage = 0;
      fs_last_seq = rtp_seq;
    }
    flow->packet_stage++;
  } else if (3 == isValidMSRTPType(payload[1] & 0xFF)) {
    global_logger.Info("identifyProto = RTP");
    flow->real_protocol_id = IPC_PROTOCOL_RTP;
    fs_last_seq = rtp_seq;
  } else {
    return;
  }
}

void RtpKeeper ::dissectProto(flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  if (payload_len <= 12) {
    return;
  }
  uint16_t seq = get_uint16_ntohs(payload, 2);

  uint8_t  octet1 = get_uint8_t(payload, 0);
  uint8_t  rtp_version = CACL_RTP_VERSION(octet1);
  uint8_t  padding_set = RTP_PADDING(octet1);
  uint8_t  extension_set = RTP_EXTENSION(octet1);
  uint32_t csrc_count = RTP_CSRC_COUNT(octet1);

  uint8_t  octet2 = get_uint8_t(payload, 1);
  uint8_t  marker_set = RTP_MARKER(octet2);
  uint32_t payload_type = RTP_PAYLOAD_TYPE(octet2);

  uint16_t seq_num = get_uint16_ntohs(payload, 2);
  uint32_t timestamp = get_uint32_ntohl(payload, 4);
  uint32_t sync_src = get_uint32_ntohl(payload, 8);

  std::shared_ptr<RtpStream> stream = nullptr;

  RtpFlowKey rtpKey;
  rtpKey.port_dst = flow->tuple.port_dst;
  rtpKey.port_src = flow->tuple.port_src;
  rtpKey.payload_type = payload_type;
  rtpKey.ssrc = sync_src;

  auto it = map_streams_.find(rtpKey);
  if (it != map_streams_.end()) {
    stream = it->second;
  } else {
    RtpMediaType MediaType = getRtpMediaType(flow, payload_type, sync_src);
    if (MediaType == RtpMediaType::unknown) {
      return;
    }
    auto newStreamPtr = std::make_shared<RtpStream>(rtpKey, flow);
    if (newStreamPtr == nullptr) {
      global_logger.Debug("std::make_shared<RtpStream>()");
      return;
    }
    stream = newStreamPtr;
    newStreamPtr->payload_type = payload_type;
    map_streams_[rtpKey] = newStreamPtr;
  }
  stream->enqueRtpStreamFragment((uint8_t *)payload + 12, payload_len - 12);
}

void RtpKeeper ::foreachRtpStreamer(std::function<int(RtpStream *)> func) {
  for (auto &pair : map_streams_) {
    func(pair.second.get());
  }

  return;
}

RtpMediaType RtpKeeper::getRtpMediaType(flow_info *flow, uint8_t payload_type, uint32_t ssrc) {
  //rtsp关联的h264
  auto findIter = map_ssrc2rtpMediaInfo_.find(ssrc);
  if (findIter != map_ssrc2rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findIter->second;
    return minfo.mediaType;
  }

  //sip关联的h264
  RtpPortInfo rtpPort;
  rtpPort.port_dst = ntohs(flow->tuple.port_dst);
  rtpPort.port_src = ntohs(flow->tuple.port_src);
  rtpPort.payload_type = payload_type;

  auto findPortIter_src = map_port2rtpMediaInfo_.find(rtpPort);
  if (findPortIter_src != map_port2rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findPortIter_src->second;
    auto         decoder = static_cast<ipc_decoder *>(flow->user);
    decoder->creatIpcStreamFromSip(minfo.sipStream);
    decoder->creatIpcStreamFromRTSP(minfo.rtspStream, flow, ssrc, minfo.mediaType, NULL, 0, NULL, 0);
    return minfo.mediaType;
  }
  if(flow->create_by == IPC_PROTOCOL_RTSP){
    return flow->create_by_mediaType;
  }
  return RtpMediaType::unknown;
}

RtpMediaType RtpKeeper::getRtpMediaType(RtpStream &stream) {
  guint8 type = stream.payload_type;

  switch (type) {
    case PT_PCMU:
    case PT_GSM:
    case PT_PCMA:  // g711
    case PT_G721:
    case PT_G723:
    case PT_G722:
    case PT_G728:
    case PT_G729:

      return RtpMediaType::audio;
    case PT_H261:
      return RtpMediaType::video_h261;
    case PT_H263:
      return RtpMediaType::video_h263;
  }

  //rtsp关联的h264
  auto findIter = map_ssrc2rtpMediaInfo_.find(stream.ssrc);
  if (findIter != map_ssrc2rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findIter->second;
    return minfo.mediaType;
  }

  //sip关联的h264
  RtpPortInfo rtpPort;
  rtpPort.port_dst = ntohs(stream.tuple.port_dst);
  rtpPort.port_src = ntohs(stream.tuple.port_src);
  rtpPort.payload_type = stream.payload_type;

  auto findPortIter_src = map_port2rtpMediaInfo_.find(rtpPort);
  if (findPortIter_src != map_port2rtpMediaInfo_.end()) {
    RtpMediaInfo minfo = findPortIter_src->second;
    auto         decoder = static_cast<ipc_decoder *>(stream.user);
    decoder->creatIpcStreamFromSip(minfo.sipStream);
    return minfo.mediaType;
  }

  return RtpMediaType::unknown;
}
