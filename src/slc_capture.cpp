#include "slc_common.h"
#include <string>

#ifdef __cplusplus
extern "C" {
#endif
#include <pthread.h>
#include <pcap.h>  // for libpcap/WinPcap calls
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

#define CHECK_NOR_EXIT(cond, errMsg, retCode) \
  if ((cond)) {                               \
    fprintf(stderr, "%s\n", errMsg);          \
    return (retCode);                         \
  }
void mkdirs(const char *muldir) {
  if (NULL == muldir) {
    return;
  }
  if (access(muldir, 0) == 0) {
    return;
  }
  int  i, len;
  char str[512];

  strncpy(str, muldir, 512);
  len = strlen(str);
  for (i = 0; i < len; i++) {
    if (str[i] == '/') {
      str[i] = '\0';
      if (access(str, 0) != 0) {
        mkdir(str, 0777);
      }
      str[i] = '/';
    }
  }
  if (len > 0 && access(str, 0) != 0) {
    mkdir(str, 0777);
  }

  return;
}
#ifdef __cplusplus
}
#endif
#include "slc_capture.h"
#ifndef SOCKET
#define SOCKET int
#endif

int update_yaSlc_status(yaslc_status_t update_status) {
  static yaslc_status_t status;
  status.status = update_status.status;
  status.type = update_status.type;
  // status.rtsp_addr = update_status.rtsp_addr;

  const char *yaSlc_status_file = "yaSlc_status";
  FILE       *fp = fopen(yaSlc_status_file, "w");
  std::string file_content = "PROTOCOL_TYP  = \"" + std::to_string(status.type) + "\"" +
                             "      #UNKNOWN = 0 RTSP = 1 SIP = 2 RTP = "
                             "3 DHAV = 4\n" +
                             "YASLC_STATUS  = \"" + std::to_string(status.status) +
                             "\"     #YASLC_DISSECT = 0,YASLC_NO = 1  YASLC_YES = 2 \n" + "RTSP_ADDR     = \"554/live\"\n";
  if (fp) {
    fwrite(file_content.c_str(), file_content.size(), 1, fp);
  }
  fflush(fp);
  fclose(fp);
  return 0;
}

static int on_new_stream_callback(uint64_t stream_id, void *userdata) {
  auto cap = static_cast<SlcCap *>(userdata);
  // if (cap->f == NULL) {
  //   char file_name[1024] = {0};
  //   snprintf(file_name, sizeof(file_name), "test_%lu.h264", stream_id);
  //   cap->f = fopen(file_name, "wb+");
  //   if (!cap->f) {
  //     exit(0);
  //   }
  // }
  uint64_t          ret = 0;
  struct context_t *context = (struct context_t *)userdata;

  // printf("on_new_stream_callback = %lu\n", stream_id);

  ret = ipc_deocder_get_stream_capability(cap->decoder_handle, stream_id);

  if (ret & IPC_CAPABILITY_ADVANCED_VIDEO_CODING) {
    int i = ipc_deocder_get_advanced_video_coding(cap->decoder_handle, stream_id);
    // printf("advanced_video_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_ADVANCED_AUDIO_CODING) {
    int i = ipc_deocder_get_advanced_audio_coding(cap->decoder_handle, stream_id);
    // printf("advanced_audio_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_DEVICEID) {
    uint8_t buff[1024] = {0};
    ipc_deocder_get_deviceid(cap->decoder_handle, stream_id, buff, sizeof(buff));
    // printf("devideid = %s\n", buff);
  }

  IPC_PROTOCOL_TYPE type = IPC_PROTOCOL_UNKNOWN;
  if (ret & IPC_CAPABILITY_IPC_PROTO_TYPE) {
    type = ipc_deocder_get_ipc_protocol(cap->decoder_handle, stream_id);
    // printf("type = %d\n", type);
  }
  if (ret & IPC_CAPABILITY_SAMPLE_RATE) {
    int rate = ipc_deocder_get_stream_sample_rate(cap->decoder_handle, stream_id);
    // printf("rate = %d\n", rate);
  }
  yaslc_status_t status;
  status.type = type;
  if (type == IPC_PROTOCOL_UNKNOWN) {
    return 0;
  } else {
    status.status = YASLC_YES;
  }
  update_yaSlc_status(status);

}
static int on_new_nalu_callback(
    uint64_t stream_id, uint64_t seq, struct ipc_nalu_t *nalu_head, uint64_t nalu_len, void *userdata) {
  // printf("on_new_nalu_callback len %llu\n", nalu_len);
  auto cap = static_cast<SlcCap *>(userdata);
  if (cap->f != NULL) {
    fwrite("\x00\x00\x00\x01", 4, 1, cap->f);
    fwrite((uint8_t *)nalu_head, nalu_len, 1, cap->f);
    fflush(cap->f);
  }

  xop::AVFrame videoFrame = {0};
  videoFrame.type = 0;
  videoFrame.size = nalu_len;
  videoFrame.timestamp = xop::H264Source::GetTimestamp();
  videoFrame.buffer.reset(new uint8_t[videoFrame.size]);
  memcpy(videoFrame.buffer.get(), nalu_head, videoFrame.size);
  cap->rtsp_server_->PushFrame(cap->session_id_, xop::channel_0, videoFrame);

  return 0;
}

struct thread_session {
  SOCKET       sockctrl;
  SOCKET       sockdata;
  uint8_t      protocol_version;
  unsigned int TotCapt;
  int          have_thread;
  pthread_t    pthread;
  pcap_t      *handle;  //libpcap句柄
};

struct thread_session flow_thread_info;

SlcCap::SlcCap(const std::string &strSlcCapFilter) : strSlcCapFilter_(strSlcCapFilter) {
  //创建config句柄
  IPC_CONFIG *config_handle = ipc_create_config(on_new_stream_callback, on_new_nalu_callback);

  // 设置上下文
  ipc_cfg_set_user_data(config_handle, this);

  //设置捕获网络类型
  IPC_FRAME_TYPE_E type = (IPC_FRAME_TYPE_E)ARM_DEVICE;
  ipc_cfg_set_config_frame_type(config_handle, type);

  //设置排除的网络报文ip
  const char *pStrValue = RTSP_SERVER_IP;
  uint32_t    excludeIP_ = ntohl(inet_addr(pStrValue));
  ipc_cfg_set_exclude_packet_ip(config_handle, excludeIP_);

  //设置日志等级
  int log_level = LOG_LEVEL;
  ipc_cfg_set_config_log_level(config_handle, (enum IPC_LOG_LEVEL_E)log_level);

  //创建decoder句柄
  decoder_handle = ipc_create_decoder(config_handle);
}

SlcCap::~SlcCap() {}

void SlcCap::onGotLivePkt(uint8_t *user, const struct pcap_pkthdr *h, const u_char *bytes) {
  if (NULL == user) {
    return;
  }
  ((SlcCap *)user)->parsePcapPkt(&h->ts, 0, h->caplen, bytes);
}

std::string SlcCap::GenerateCapFileName() {
  static char    time_str[64];
  struct tm      tm_tmp;
  struct timeval tv;
  gettimeofday(&tv, NULL);

  localtime_r(&tv.tv_sec, &tm_tmp);
  strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &tm_tmp);

  std::string filename = filepath_ + "/" + brand_ + "_" + model_ + "_" + orb_ + "_" + time_str + ".pcap";
  return filename;
}

int SlcCap::parseSlcCapture() {
  const char *ini_name = "yaSlc_capture";
  dictionary *ini_ = iniparser_load(ini_name);

  if (ini_ == NULL) {
    fprintf(stderr, "cannot parse file: %s\n", ini_name);
    return -1;
  }
  std::string brand(iniparser_getstring(ini_, ":BRAND", ""));
  std::string model(iniparser_getstring(ini_, ":MODEL", ""));
  std::string orb(iniparser_getstring(ini_, ":ORB", ""));
  capMaxFileSize_ = iniparser_getint(ini_, ":FILESIZE", 100);
  filepath_ = iniparser_getstring(ini_, ":FILE_PATH", "./");
  if (access(filepath_.c_str(), 0) != 0) {
    mkdirs(filepath_.c_str());
  }
  capMaxFileSize_ = capMaxFileSize_ * 1024 * 1024;
  brand_ = brand;
  model_ = model;
  orb_ = orb;

  setCapFileName(GenerateCapFileName().c_str());
  iniparser_freedict(ini_);

  return 0;
}

void SlcCap::clearCapInfo() {
  brand_.clear();
  model_.clear();
  orb_.clear();
  capFileName.clear();
  capFileNameFinish.clear();
  capFileSize_ = 0;
  capFileFp = NULL;
}

void SlcCap::setCapFileSwitch(int cap_switch) {
  if (cap_switch == cap_switch_) {
    //重复动作
    return;
  } else {
    if (cap_switch) {
      parseSlcCapture();
    } else {
      saveFileStop();
    }
    cap_switch_ = cap_switch;
  }
}

int SlcCap::saveFileStart() {
  if (capFileName.size() == 0) {
    GenerateCapFileName();
  }
  if (capFileFp == NULL) {
    struct stat st;
    capFileFp = fopen(capFileName.c_str(), "w");
    if (capFileFp) {
      struct pcap_file_header fh;
      memset(&fh, 0, sizeof(struct pcap_file_header));
      fh.magic = 0xA1B2C3D4;
      fh.version_major = PCAP_VERSION_MAJOR;
      fh.version_minor = PCAP_VERSION_MINOR;
      fh.linktype = DLT_EN10MB;
      fh.snaplen = 0XFFFFFFFF;
      fwrite(&fh, sizeof(fh), 1, capFileFp);
    } else {
      return -1;
    }
  }
  return 1;
}

void SlcCap::saveFileStop() {
  if (capFileFp) {
    fclose(capFileFp);
    rename(capFileName.c_str(),capFileNameFinish.c_str());
    clearCapInfo();
    setCapFileSwitch(0);
  }
  parseSlcCapture();
}

void SlcCap::saveFile(const uint8_t *pPktData, int len) {
  int ret = 0;
  if(capFileSize_ == 0 || capFileFp == NULL){
    ret = saveFileStart();
  }
  if (capFileSize_ > capMaxFileSize_) {
    //文件开始或轮换
    saveFileStop();
    // ret = saveFileStart();
  }
  if (ret == -1 ||capFileFp == NULL) {
    return;
  }
  struct pcappkt_hdr pckt_header;
  int                wcnt = 0, nwrt = 0;
  struct timeval     tv;
  gettimeofday(&tv, NULL);
  pckt_header.caplen = len;
  pckt_header.len = len;
  pckt_header.tv_sec = tv.tv_sec;
  pckt_header.tv_usec = tv.tv_usec;

  //写pcap头
  do {
    nwrt = fwrite(((char *)&pckt_header) + wcnt, 1, sizeof(struct pcappkt_hdr) - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != sizeof(struct pcappkt_hdr));

  wcnt = 0;
  do {
    nwrt = fwrite(pPktData + wcnt, 1, len - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != len);
  capFileSize_ = ftell(capFileFp);
}

void SlcCap::parsePcapPkt(const timeval *pCapTs, int pktNO, int len, const uint8_t *pPktData) {
  ipc_process_packet(decoder_handle, pPktData, len);
  if (cap_switch_) {
    saveFile(pPktData, len);
  }
}

int SlcCap::inspectLiveCap(const char *pStrIf) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0, count = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);
  /* Open the session in promiscuous mode */
  int bufsize = BUFSIZ;
  pcapHandle = pcap_open_live(pStrIf, 2000, 1, -1, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, "Couldn't open if.\n", -1);

  // loop
  pcap_loop(pcapHandle, 0, SlcCap::onGotLivePkt, (u_char *)this);

  pcap_close(pcapHandle);
  return 0;
}

int SlcCap::inspectPcapFile(const char *pPcapFileName) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);

  pcap_pkthdr   *pkt_header = NULL;
  const uint8_t *pkt_data = NULL;

  // 打开 pcap 文件
  pcapHandle = pcap_open_offline(pPcapFileName, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, errbuf, -1);

  /* pcap 过滤条件 */
  lSts = pcap_compile(pcapHandle, &fp, filter_exp, 0, 0);
  CHECK_NOR_EXIT(-1 == lSts, "Couldn't parse filter", -1);
  lSts = pcap_setfilter(pcapHandle, &fp);
  CHECK_NOR_EXIT(-1 == lSts, "Counldn't install filter", -1);

  // go ...
  printf("process pcap %s ... ", pPcapFileName);
  fflush(stdout);

  // process pkts
  for (int i = 1;; i++) {
    lSts = pcap_next_ex(pcapHandle, &pkt_header, &pkt_data);
    if (0 == lSts || 1 == lSts) {  // OK, parse pkt
      parsePcapPkt(&pkt_header->ts, i, pkt_header->caplen, pkt_data);
    } else if (-1 == lSts) {  // error
      fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
      break;
    } else if (-2 == lSts) {  // no more pkt to read from offline file
      printf("done.\n");
      break;
    }
  }

  pcap_freecode(&fp);
  pcap_close(pcapHandle);
  return 0;
}
