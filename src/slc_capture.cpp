#include "slc_common.h"
#include <string>

#ifdef __cplusplus
extern "C" {
#endif
#include <pthread.h>
#include <pcap.h>  // for libpcap/WinPcap calls
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>

#define CHECK_NOR_EXIT(cond, errMsg, retCode) \
  if ((cond)) {                               \
    fprintf(stderr, "%s\n", errMsg);          \
    return (retCode);                         \
  }
void mkdirs(const char *muldir) {
  if (NULL == muldir) {
    return;
  }
  if (access(muldir, 0) == 0) {
    return;
  }
  int  i, len;
  char str[512];

  strncpy(str, muldir, 512);
  len = strlen(str);
  for (i = 0; i < len; i++) {
    if (str[i] == '/') {
      str[i] = '\0';
      if (access(str, 0) != 0) {
        mkdir(str, 0777);
      }
      str[i] = '/';
    }
  }
  if (len > 0 && access(str, 0) != 0) {
    mkdir(str, 0777);
  }

  return;
}
#ifdef __cplusplus
}
#endif
#include "slc_capture.h"
#ifndef SOCKET
#define SOCKET int
#endif

int update_yaSlc_status(yaslc_status_t update_status) {
  static yaslc_status_t status;
  status.status = update_status.status;
  status.type = update_status.type;

  const char *yaSlc_status_file = "yaSlc_status";
  FILE       *fp = fopen(yaSlc_status_file, "w");
  std::string file_content = "PROTOCOL_TYP  = \"" + std::to_string(status.type) + "\"" +
                             "      #UNKNOWN = 0 RTSP = 1 SIP = 2 RTP = "
                             "3 DHAV = 4\n" +
                             "YASLC_STATUS  = \"" + std::to_string(status.status) +
                             "\"     #YASLC_DISSECT = 0,YASLC_NO = 1  YASLC_YES = 2 \n";
  if (fp) {
    fwrite(file_content.c_str(), file_content.size(), 1, fp);
  }
  fflush(fp);
  fclose(fp);
  return 0;
}

static int on_new_stream_callback(uint64_t stream_id, void *userdata) {
  auto cap = static_cast<SlcCap *>(userdata);
  // if (cap->f == NULL) {
  //   char file_name[1024] = {0};
  //   snprintf(file_name, sizeof(file_name), "test_%lu.h264", stream_id);
  //   cap->f = fopen(file_name, "wb+");
  //   if (!cap->f) {
  //     exit(0);
  //   }
  // }
  uint64_t          ret = 0;
  struct context_t *context = (struct context_t *)userdata;

  // printf("on_new_stream_callback = %lu\n", stream_id);

  ret = ipc_deocder_get_stream_capability(cap->decoder_handle, stream_id);

  if (ret & IPC_CAPABILITY_ADVANCED_VIDEO_CODING) {
    int i = ipc_deocder_get_advanced_video_coding(cap->decoder_handle, stream_id);
    // printf("advanced_video_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_ADVANCED_AUDIO_CODING) {
    int i = ipc_deocder_get_advanced_audio_coding(cap->decoder_handle, stream_id);
    // printf("advanced_audio_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_DEVICEID) {
    uint8_t buff[1024] = {0};
    ipc_deocder_get_deviceid(cap->decoder_handle, stream_id, buff, sizeof(buff));
    // printf("devideid = %s\n", buff);
  }

  IPC_PROTOCOL_TYPE type = IPC_PROTOCOL_UNKNOWN;
  if (ret & IPC_CAPABILITY_IPC_PROTO_TYPE) {
    type = ipc_deocder_get_ipc_protocol(cap->decoder_handle, stream_id);
    // printf("type = %d\n", type);
  }
  if (ret & IPC_CAPABILITY_SAMPLE_RATE) {
    int rate = ipc_deocder_get_stream_sample_rate(cap->decoder_handle, stream_id);
    // printf("rate = %d\n", rate);
  }
  yaslc_status_t status;
  status.type = type;
  if (type == IPC_PROTOCOL_UNKNOWN) {
    return 0;
  } else {
    status.status = YASLC_YES;
  }
  update_yaSlc_status(status);

  // 通过Unix Socket发送状态更新到客户端
  cap->sendStatusToClients(status);

  return 0;
}
static int on_new_nalu_callback(
    uint64_t stream_id, uint64_t seq, struct ipc_nalu_t *nalu_head, uint64_t nalu_len, void *userdata) {
  // printf("on_new_nalu_callback len %llu\n", nalu_len);
  auto cap = static_cast<SlcCap *>(userdata);
  if (cap->f != NULL) {
    fwrite("\x00\x00\x00\x01", 4, 1, cap->f);
    fwrite((uint8_t *)nalu_head, nalu_len, 1, cap->f);
    fflush(cap->f);
  }

  // 通过Unix Socket发送NALU数据到客户端
  cap->sendNaluDataToClients((const uint8_t*)nalu_head, nalu_len);

  return 0;
}

struct thread_session {
  SOCKET       sockctrl;
  SOCKET       sockdata;
  uint8_t      protocol_version;
  unsigned int TotCapt;
  int          have_thread;
  pthread_t    pthread;
  pcap_t      *handle;  //libpcap句柄
};

struct thread_session flow_thread_info;

SlcCap::SlcCap(const std::string &strSlcCapFilter, const std::string &plugin_path, const std::string &dump_path, bool enable_dump, const std::string &socket_path)
    : strSlcCapFilter_(strSlcCapFilter), plugin_loader_(nullptr), decoder_handle(nullptr),
      dump_file_(nullptr), enable_dump_(enable_dump), dump_path_(dump_path),
      unix_server_(nullptr), socket_path_(socket_path) {

  // 创建插件加载器
  plugin_loader_ = new PluginLoader();

  // 确定插件路径
  std::string actual_plugin_path = plugin_path;
  if (actual_plugin_path.empty()) {
    // 如果没有指定插件路径，使用默认路径
    actual_plugin_path = "./libipc.so";
  }

  // 加载插件
  if (plugin_loader_->load_plugin(actual_plugin_path) != 0) {
    std::cerr << "Failed to load plugin: " << actual_plugin_path << std::endl;
    return;
  }

  const struct plugin_interface* iface = plugin_loader_->get_interface();
  if (!iface) {
    std::cerr << "Failed to get plugin interface" << std::endl;
    return;
  }

  //创建config句柄
  IPC_CONFIG *config_handle = iface->ipc_create_config(on_new_stream_callback, on_new_nalu_callback);

  // 设置上下文
  iface->ipc_cfg_set_user_data(config_handle, this);

  //设置捕获网络类型
  IPC_FRAME_TYPE_E type = (IPC_FRAME_TYPE_E)ARM_DEVICE;
  iface->ipc_cfg_set_config_frame_type(config_handle, type);

  //设置排除的网络报文ip
  const char *pStrValue = RTSP_SERVER_IP;
  uint32_t    excludeIP_ = ntohl(inet_addr(pStrValue));
  iface->ipc_cfg_set_exclude_packet_ip(config_handle, excludeIP_);

  //设置日志等级
  int log_level = LOG_LEVEL;
  iface->ipc_cfg_set_config_log_level(config_handle, (enum IPC_LOG_LEVEL_E)log_level);

  //创建decoder句柄
  decoder_handle = iface->ipc_create_decoder(config_handle);

  // 初始化dump文件
  if (enable_dump_ && !dump_path_.empty()) {
    dump_file_ = fopen(dump_path_.c_str(), "wb");
    if (!dump_file_) {
      std::cerr << "Failed to open dump file: " << dump_path_ << std::endl;
      enable_dump_ = false;
    } else {
      std::cout << "Dump enabled, writing to: " << dump_path_ << std::endl;
    }
  }

  // 初始化Unix Socket服务器
  startUnixSocketServer();
}

SlcCap::~SlcCap() {
  if (plugin_loader_) {
    if (decoder_handle && plugin_loader_->is_loaded()) {
      const struct plugin_interface* iface = plugin_loader_->get_interface();
      if (iface && iface->ipc_destroy_decoder) {
        iface->ipc_destroy_decoder(decoder_handle);
      }
    }
    delete plugin_loader_;
    plugin_loader_ = nullptr;
  }
  decoder_handle = nullptr;

  // 关闭dump文件
  if (dump_file_) {
    fclose(dump_file_);
    dump_file_ = nullptr;
  }

  // 停止Unix Socket服务器
  stopUnixSocketServer();

  // 打印内存使用报告
  MemoryMonitor::getInstance().printReport();
}

void SlcCap::onGotLivePkt(uint8_t *user, const struct pcap_pkthdr *h, const u_char *bytes) {
  if (NULL == user) {
    return;
  }
  ((SlcCap *)user)->parsePcapPkt(&h->ts, 0, h->caplen, bytes);
}

std::string SlcCap::GenerateCapFileName() {
  static char    time_str[64];
  struct tm      tm_tmp;
  struct timeval tv;
  gettimeofday(&tv, NULL);

  localtime_r(&tv.tv_sec, &tm_tmp);
  strftime(time_str, sizeof(time_str), "%Y%m%d%H%M%S", &tm_tmp);

  std::string filename = filepath_ + "/" + brand_ + "_" + model_ + "_" + orb_ + "_" + time_str + ".pcap";
  return filename;
}

int SlcCap::parseSlcCapture() {
  const char *ini_name = "yaSlc_capture";
  dictionary *ini_ = iniparser_load(ini_name);

  if (ini_ == NULL) {
    fprintf(stderr, "cannot parse file: %s\n", ini_name);
    return -1;
  }
  std::string brand(iniparser_getstring(ini_, ":BRAND", ""));
  std::string model(iniparser_getstring(ini_, ":MODEL", ""));
  std::string orb(iniparser_getstring(ini_, ":ORB", ""));
  capMaxFileSize_ = iniparser_getint(ini_, ":FILESIZE", 100);
  filepath_ = iniparser_getstring(ini_, ":FILE_PATH", "./");
  if (access(filepath_.c_str(), 0) != 0) {
    mkdirs(filepath_.c_str());
  }
  capMaxFileSize_ = capMaxFileSize_ * 1024 * 1024;
  brand_ = brand;
  model_ = model;
  orb_ = orb;

  setCapFileName(GenerateCapFileName().c_str());
  iniparser_freedict(ini_);

  return 0;
}

void SlcCap::clearCapInfo() {
  brand_.clear();
  model_.clear();
  orb_.clear();
  capFileName.clear();
  capFileNameFinish.clear();
  capFileSize_ = 0;
  capFileFp = NULL;
}

void SlcCap::setCapFileSwitch(int cap_switch) {
  if (cap_switch == cap_switch_) {
    //重复动作
    return;
  } else {
    if (cap_switch) {
      parseSlcCapture();
    } else {
      saveFileStop();
    }
    cap_switch_ = cap_switch;
  }
}

int SlcCap::saveFileStart() {
  if (capFileName.size() == 0) {
    GenerateCapFileName();
  }
  if (capFileFp == NULL) {
    struct stat st;
    capFileFp = fopen(capFileName.c_str(), "w");
    if (capFileFp) {
      struct pcap_file_header fh;
      memset(&fh, 0, sizeof(struct pcap_file_header));
      fh.magic = 0xA1B2C3D4;
      fh.version_major = PCAP_VERSION_MAJOR;
      fh.version_minor = PCAP_VERSION_MINOR;
      fh.linktype = DLT_EN10MB;
      fh.snaplen = 0XFFFFFFFF;
      fwrite(&fh, sizeof(fh), 1, capFileFp);
    } else {
      return -1;
    }
  }
  return 1;
}

void SlcCap::saveFileStop() {
  if (capFileFp) {
    fclose(capFileFp);
    rename(capFileName.c_str(),capFileNameFinish.c_str());
    clearCapInfo();
    setCapFileSwitch(0);
  }
  parseSlcCapture();
}

void SlcCap::saveFile(const uint8_t *pPktData, int len) {
  int ret = 0;
  if(capFileSize_ == 0 || capFileFp == NULL){
    ret = saveFileStart();
  }
  if (capFileSize_ > capMaxFileSize_) {
    //文件开始或轮换
    saveFileStop();
    // ret = saveFileStart();
  }
  if (ret == -1 ||capFileFp == NULL) {
    return;
  }
  struct pcappkt_hdr pckt_header;
  int                wcnt = 0, nwrt = 0;
  struct timeval     tv;
  gettimeofday(&tv, NULL);
  pckt_header.caplen = len;
  pckt_header.len = len;
  pckt_header.tv_sec = tv.tv_sec;
  pckt_header.tv_usec = tv.tv_usec;

  //写pcap头
  do {
    nwrt = fwrite(((char *)&pckt_header) + wcnt, 1, sizeof(struct pcappkt_hdr) - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != sizeof(struct pcappkt_hdr));

  wcnt = 0;
  do {
    nwrt = fwrite(pPktData + wcnt, 1, len - wcnt, capFileFp);
    fflush(capFileFp);
    if (nwrt > 0)
      wcnt += nwrt;
    else
      break;
  } while (wcnt != len);
  capFileSize_ = ftell(capFileFp);
}

void SlcCap::parsePcapPkt(const timeval *pCapTs, int pktNO, int len, const uint8_t *pPktData) {
  if (plugin_loader_ && plugin_loader_->is_loaded() && decoder_handle) {
    const struct plugin_interface* iface = plugin_loader_->get_interface();
    if (iface && iface->ipc_process_packet) {
      iface->ipc_process_packet(decoder_handle, pPktData, len);
    }
  }
  if (cap_switch_) {
    saveFile(pPktData, len);
  }
}

int SlcCap::inspectLiveCap(const char *pStrIf) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0, count = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);
  /* Open the session in promiscuous mode */
  int bufsize = BUFSIZ;
  pcapHandle = pcap_open_live(pStrIf, 2000, 1, -1, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, "Couldn't open if.\n", -1);

  // loop
  pcap_loop(pcapHandle, 0, SlcCap::onGotLivePkt, (u_char *)this);

  pcap_close(pcapHandle);
  return 0;
}

int SlcCap::inspectPcapFile(const char *pPcapFileName) {
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  char        filter_exp[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0;

  strncpy(filter_exp, strSlcCapFilter_.c_str(), sizeof filter_exp);

  pcap_pkthdr   *pkt_header = NULL;
  const uint8_t *pkt_data = NULL;

  // 打开 pcap 文件
  pcapHandle = pcap_open_offline(pPcapFileName, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, errbuf, -1);

  /* pcap 过滤条件 */
  lSts = pcap_compile(pcapHandle, &fp, filter_exp, 0, 0);
  CHECK_NOR_EXIT(-1 == lSts, "Couldn't parse filter", -1);
  lSts = pcap_setfilter(pcapHandle, &fp);
  CHECK_NOR_EXIT(-1 == lSts, "Counldn't install filter", -1);

  // go ...
  printf("process pcap %s ... ", pPcapFileName);
  fflush(stdout);

  // process pkts
  for (int i = 1;; i++) {
    lSts = pcap_next_ex(pcapHandle, &pkt_header, &pkt_data);
    if (0 == lSts || 1 == lSts) {  // OK, parse pkt
      parsePcapPkt(&pkt_header->ts, i, pkt_header->caplen, pkt_data);
    } else if (-1 == lSts) {  // error
      fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
      break;
    } else if (-2 == lSts) {  // no more pkt to read from offline file
      printf("done.\n");
      break;
    }
  }

  pcap_freecode(&fp);
  pcap_close(pcapHandle);
  return 0;
}

// Unix Socket相关方法实现
void SlcCap::startUnixSocketServer() {
  if (unix_server_) {
    return; // 已经启动
  }

  unix_server_ = new UnixSocketServer(socket_path_);

  // 设置消息处理回调
  unix_server_->set_message_handler([this](uint32_t msg_type, const uint8_t* data, uint32_t length, int client_fd) {
    // 处理来自客户端的消息
    switch (msg_type) {
      case MSG_TYPE_CONTROL:
        // 处理控制消息
        std::cout << "Received control message from client " << client_fd << std::endl;
        break;
      case MSG_TYPE_HEARTBEAT:
        // 处理心跳消息
        // 回复心跳
        unix_server_->send_message(client_fd, MSG_TYPE_HEARTBEAT, nullptr, 0);
        break;
      default:
        std::cout << "Received unknown message type: " << msg_type << std::endl;
        break;
    }
  });

  if (!unix_server_->start()) {
    std::cerr << "Failed to start Unix Socket server" << std::endl;
    delete unix_server_;
    unix_server_ = nullptr;
  }
}

void SlcCap::stopUnixSocketServer() {
  if (unix_server_) {
    unix_server_->stop();
    delete unix_server_;
    unix_server_ = nullptr;
    std::cout << "Unix Socket server stopped" << std::endl;
  }
}

void SlcCap::sendNaluDataToClients(const uint8_t* nalu_data, uint32_t length) {
  if (unix_server_ && nalu_data && length > 0) {
    unix_server_->broadcast_message(MSG_TYPE_NALU_DATA, nalu_data, length);
  }
}

void SlcCap::sendStatusToClients(const yaslc_status_t& status) {
  if (unix_server_) {
    // 将状态结构体序列化为字节数组
    uint8_t status_data[sizeof(yaslc_status_t)];
    memcpy(status_data, &status, sizeof(yaslc_status_t));
    unix_server_->broadcast_message(MSG_TYPE_STATUS, status_data, sizeof(yaslc_status_t));
  }
}
