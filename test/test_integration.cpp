#include <iostream>
#include <string>
#include <cstdlib>
#include <unistd.h>
#include <sys/wait.h>

// 简化的集成测试
int main() {
    std::cout << "系统集成基础测试" << std::endl;
    std::cout << "================" << std::endl;

    // 测试主程序是否存在
    if (access("./run/yaslc", F_OK) == 0) {
        std::cout << "✅ 主程序 yaslc 存在" << std::endl;
    } else {
        std::cout << "❌ 主程序 yaslc 不存在" << std::endl;
        return 1;
    }

    // 测试帮助功能
    int result = system("./run/yaslc --help > /dev/null 2>&1");
    if (WEXITSTATUS(result) == 0) {
        std::cout << "✅ 主程序帮助功能正常" << std::endl;
    } else {
        std::cout << "❌ 主程序帮助功能异常" << std::endl;
        return 1;
    }

    std::cout << "✅ 系统集成基础测试通过" << std::endl;
    return 0;
}

