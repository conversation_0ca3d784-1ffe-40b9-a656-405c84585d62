#include <map>
#include <pcap.h>
#include <string.h>
#include <cstdlib>

#include "../include/ipc/ipc.h"

IPC_DECODER *decoder_handle = NULL;
// check_nor_exit
#define CHECK_NOR_EXIT(cond, errMsg, retCode) \
  if ((cond)) {                               \
    fprintf(stderr, "%s\n", errMsg);          \
    return (retCode);                         \
  }

struct file_cont {
  FILE    *file;
  uint64_t stream_id;
};
struct context_t {
  struct file_cont fc[10];
  int              fc_num;
};

int on_new_stream_callback(uint64_t stream_id, void *userdata) {
  uint64_t          ret = 0;
  struct context_t *context = (struct context_t *)userdata;

  if (context->fc[context->fc_num].file == NULL) {
    char file_name[1024] = {0};
    snprintf(file_name, sizeof(file_name), "%lu.h264", stream_id);
    context->fc[context->fc_num].file = fopen(file_name, "wb+");
    context->fc[context->fc_num].stream_id = stream_id;
    if (!context->fc[context->fc_num].file) {
      exit(0);
    }
  }

  context->fc_num++;
  printf("on_new_stream_callback = %lu\n", stream_id);

  ret = ipc_deocder_get_stream_capability(decoder_handle, stream_id);

  if (ret & IPC_CAPABILITY_ADVANCED_VIDEO_CODING) {
    int i = ipc_deocder_get_advanced_video_coding(decoder_handle, stream_id);
    printf("advanced_video_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_ADVANCED_AUDIO_CODING) {
    int i = ipc_deocder_get_advanced_audio_coding(decoder_handle, stream_id);
    printf("advanced_audio_coding = %d\n", i);
  }

  if (ret & IPC_CAPABILITY_DEVICEID) {
    uint8_t buff[1024] = {0};
    ipc_deocder_get_deviceid(decoder_handle, stream_id, buff, sizeof(buff));
    printf("devideid = %s\n", buff);
  }

  if (ret & IPC_CAPABILITY_IPC_PROTO_TYPE) {
    IPC_PROTOCOL_TYPE type = IPC_PROTOCOL_UNKNOWN;
    type = ipc_deocder_get_ipc_protocol(decoder_handle, stream_id);
    printf("type = %d\n", type);
  }
  if (ret & IPC_CAPABILITY_SAMPLE_RATE) {
    int rate = ipc_deocder_get_stream_sample_rate(decoder_handle, stream_id);
    printf("rate = %d\n", rate);
  }

  return 0;
}

int on_new_nalu_callback(uint64_t stream_id, uint64_t seq, struct ipc_nalu_t *nalu_head, uint64_t nalu_len, void *userdata) {
  //send_to_
  printf("on_new_nalu_callback stream = %lu  nalu_len = %ld\n", stream_id, nalu_len);

  struct context_t *context = (struct context_t *)userdata;
  for (int i = 0; i < context->fc_num; i++)
    if (context->fc[i].stream_id == stream_id) {
      FILE *file = context->fc[i].file;
      fwrite("\x00\x00\x00\x01", 4, 1, file);
      fwrite((uint8_t *)nalu_head, nalu_len, 1, file);
    }
  return 0;
}
int on_miss_nalu_callback(int stream_id, int miss_len, void *userdata) { return 0; }

void packet_handler(u_char *user, const struct pcap_pkthdr *packethdr, const u_char *packetptr) {
  int          ret = 0;
  IPC_DECODER *decoder_handle = user;
  printf("read len = %d", packethdr->caplen);
  ret = ipc_process_packet(decoder_handle, packetptr, packethdr->caplen);
  return;
}

int main() {
  struct context_t context;
  memset(&context, 0, sizeof(context));
  bpf_program fp;
  char        errbuf[PCAP_ERRBUF_SIZE] = {0};
  pcap_t     *pcapHandle = NULL;
  int         lSts = 0, count = 0;
  IPC_CONFIG *config_handle = ipc_create_config(on_new_stream_callback, on_new_nalu_callback);
  ipc_cfg_set_user_data(config_handle, (void *)&context);
  ipc_cfg_set_config_log_level(config_handle, IPC_LOG_Error);
  /* Open the session in promiscuous mode */
  int bufsize = BUFSIZ;
  pcapHandle = pcap_open_live("br-lan", BUFSIZ, 1, 1000, errbuf);
  CHECK_NOR_EXIT(NULL == pcapHandle, "Couldn't open if.\n", -1);

  decoder_handle = ipc_create_decoder(config_handle);
  CHECK_NOR_EXIT(NULL == decoder_handle, "Couldn't init decoder.\n", -1);
  uint64_t flag = 0;
  // loop
  // Start the packet capture with a set count or continually if the count is 0.
  if (pcap_loop(pcapHandle, count, packet_handler, (u_char *)decoder_handle) < 0) {
    fprintf(stderr, "pcap_loop failed: %s\n", pcap_geterr(pcapHandle));
    return -1;
  }

  ipc_destroy_decoder(decoder_handle);
  pcap_close(pcapHandle);

  return 0;
}