#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <string>
#include <cstring>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/socket.h>
#include <sys/un.h>

// 简化测试，只测试基本功能
int main() {
    std::cout << "Unix Socket 基础测试" << std::endl;
    std::cout << "===================" << std::endl;

    // 测试Unix Socket文件创建和删除
    const char* test_socket = "/tmp/test_yaslc.sock";

    // 清理可能存在的socket文件
    unlink(test_socket);

    std::cout << "✅ Socket文件清理完成" << std::endl;

    // 测试基本的系统调用
    int test_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (test_fd >= 0) {
        std::cout << "✅ Unix Socket创建成功" << std::endl;
        close(test_fd);
    } else {
        std::cout << "❌ Unix Socket创建失败" << std::endl;
        return 1;
    }

    std::cout << "✅ Unix Socket基础测试通过" << std::endl;
    return 0;
}


