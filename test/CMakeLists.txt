cmake_minimum_required(VERSION 3.14)

# project
project(IPCFWDTest LANGUAGES C CXX VERSION 0.0.1)
option(USE_PKG_CONFIG_TO_LINK off)

set(CMAKE_C_STANDARD 17)

#
# ftyepsTest
#
add_executable(IPCFWDTest
  test.cpp
)

link_directories(CMAKE_SOURCE_DIR/lib) 

target_link_directories(IPCFWDTest PRIVATE
  ${CMAKE_SOURCE_DIR}/lib/amd64
)
target_link_libraries(IPCFWDTest
  # stdc++
  pcap
  # pthread
  ipc_0.0.2
)

# 新增测试程序

# Unix Socket 测试程序
add_executable(test_unix_socket test_unix_socket.cpp)
target_link_libraries(test_unix_socket pthread)

# Plugin Loader 测试程序
add_executable(test_plugin_loader test_plugin_loader.cpp)
target_link_libraries(test_plugin_loader dl)

# 集成测试程序
add_executable(test_integration test_integration.cpp)
target_link_libraries(test_integration)
