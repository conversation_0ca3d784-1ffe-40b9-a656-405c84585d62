cmake_minimum_required(VERSION 3.14)

# project
project(IPCFWDTest LANGUAGES C CXX VERSION 0.0.1)
option(USE_PKG_CONFIG_TO_LINK off)

set(CMAKE_C_STANDARD 17)

#
# ftyepsTest
#
add_executable(IPCFWDTest
  test.cpp
)

link_directories(CMAKE_SOURCE_DIR/lib) 

target_link_directories(IPCFWDTest PRIVATE
  ${CMAKE_SOURCE_DIR}/lib/amd64
)
target_link_libraries(IPCFWDTest
  # stdc++
  pcap
  # pthread
  ipc_0.0.2
)
